"""
主应用测试
"""
import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from app.main import app
from app.core.database import Base, get_db

# 创建测试数据库
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建测试数据库表
Base.metadata.create_all(bind=engine)


def override_get_db():
    """覆盖数据库依赖"""
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()


app.dependency_overrides[get_db] = override_get_db

client = TestClient(app)


def test_read_main():
    """测试根路径"""
    response = client.get("/")
    assert response.status_code == 200
    assert "宿舍入住管理系统" in response.text


def test_health_check():
    """测试健康检查"""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"
    assert "app_name" in data
    assert "version" in data


def test_create_department():
    """测试创建部门"""
    department_data = {
        "name": "测试部门",
        "description": "这是一个测试部门",
        "is_active": True
    }
    response = client.post("/api/v1/departments/", json=department_data)
    assert response.status_code == 201
    data = response.json()
    assert data["name"] == "测试部门"
    assert data["description"] == "这是一个测试部门"
    assert data["is_active"] is True
    assert "id" in data


def test_get_departments():
    """测试获取部门列表"""
    response = client.get("/api/v1/departments/")
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)


if __name__ == "__main__":
    pytest.main([__file__])
