# 宿舍入住管理系统 Makefile

.PHONY: help install install-dev run run-dev run-frontend test test-cov clean lint format docker-build docker-dev docker-prod docker-stop docker-clean db-init db-migrate db-backup setup

# 默认目标
help:
	@echo "🏠 宿舍入住管理系统 - 可用命令:"
	@echo ""
	@echo "📦 安装和环境:"
	@echo "  install      - 安装生产依赖"
	@echo "  install-dev  - 安装开发依赖"
	@echo "  setup        - 初始化项目环境"
	@echo ""
	@echo "🚀 运行应用:"
	@echo "  run          - 运行生产模式"
	@echo "  run-dev      - 运行开发模式（热重载）"
	@echo "  run-frontend - 运行前端开发服务器"
	@echo ""
	@echo "🧪 测试和质量:"
	@echo "  test         - 运行所有测试"
	@echo "  test-cov     - 运行测试并生成覆盖率报告"
	@echo "  lint         - 代码检查"
	@echo "  format       - 代码格式化"
	@echo ""
	@echo "🐳 Docker操作:"
	@echo "  docker-build - 构建Docker镜像"
	@echo "  docker-dev   - 启动开发环境"
	@echo "  docker-prod  - 启动生产环境"
	@echo "  docker-stop  - 停止所有容器"
	@echo "  docker-clean - 清理Docker资源"
	@echo ""
	@echo "🗄️ 数据库操作:"
	@echo "  db-init      - 初始化数据库"
	@echo "  db-migrate   - 运行数据库迁移"
	@echo "  db-backup    - 备份数据库"
	@echo ""
	@echo "🧹 清理操作:"
	@echo "  clean        - 清理临时文件"
	@echo "  clean-all    - 深度清理"

# 安装依赖
install:
	pip install -r requirements.txt

install-dev:
	pip install -r requirements.txt
	pip install pytest pytest-cov black isort flake8 mypy

# 项目初始化
setup: install-dev
	@echo "🔧 初始化项目环境..."
	mkdir -p logs uploads backups
	cp .env.example .env
	cd frontend && npm install
	@echo "✅ 项目环境初始化完成"
	@echo "📝 请编辑 .env 文件配置环境变量"

# 运行应用
run:
	uvicorn app.main:app --host 0.0.0.0 --port 8000

run-dev:
	uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

run-frontend:
	cd frontend && npm run dev

# 测试
test:
	pytest tests/ -v

test-cov:
	pytest tests/ -v --cov=app --cov-report=html --cov-report=term

# 代码质量
lint:
	flake8 app/ --max-line-length=88 --extend-ignore=E203,W503
	black --check app/
	isort --check-only app/
	mypy app/

format:
	black app/ tests/
	isort app/ tests/

# Docker操作
docker-build:
	docker build -t bedsharing-calc:latest .

docker-dev:
	docker-compose -f docker-compose.dev.yml up -d
	@echo "🚀 开发环境已启动"
	@echo "📱 前端: http://localhost:3000"
	@echo "🔧 后端: http://localhost:8000"
	@echo "📊 数据库管理: http://localhost:8080"

docker-prod:
	docker-compose -f docker-compose.prod.yml up -d
	@echo "🚀 生产环境已启动"

docker-stop:
	docker-compose -f docker-compose.dev.yml down
	docker-compose -f docker-compose.prod.yml down
	docker-compose down

docker-clean: docker-stop
	docker system prune -f
	docker volume prune -f

# 数据库操作
db-init:
	python scripts/init_data.py

db-migrate:
	alembic upgrade head

db-backup:
	@echo "🗄️ 备份数据库..."
	docker-compose exec db pg_dump -U postgres dormitory_db > backups/backup_$(shell date +%Y%m%d_%H%M%S).sql
	@echo "✅ 数据库备份完成"

# 清理操作
clean:
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	rm -rf .pytest_cache
	rm -rf htmlcov
	rm -rf .coverage
	rm -rf .mypy_cache
	find . -type f -name "*.log" -delete

clean-all: clean docker-clean
	rm -rf node_modules
	rm -rf frontend/node_modules
	rm -rf frontend/dist

# 创建虚拟环境
venv:
	python -m venv venv
	@echo "虚拟环境已创建，请运行以下命令激活:"
	@echo "source venv/bin/activate  # Linux/Mac"
	@echo "venv\\Scripts\\activate     # Windows"

# 生成requirements.txt
freeze:
	pip freeze > requirements.txt

# 检查依赖安全性
security:
	pip-audit
