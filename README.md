# 🏠 宿舍入住管理系统

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://www.python.org/)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.104+-green.svg)](https://fastapi.tiangolo.com/)
[![Vue.js](https://img.shields.io/badge/Vue.js-3.4+-4FC08D.svg)](https://vuejs.org/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

一个现代化的宿舍入住管理和费用分摊系统，采用前后端分离架构，基于FastAPI + Vue 3构建。系统提供完整的宿舍管理、住户管理、入住记录管理和智能费用分摊功能。

## ✨ 功能特性

### 🏢 核心管理功能
- **部门管理**: 支持部门的增删改查操作，部门层级管理
- **宿舍管理**: 宿舍信息和床位配置管理，支持多宿舍管理
- **住户管理**: 住户信息维护和部门关联，员工档案管理
- **入住记录**: 入住和离开记录的完整生命周期管理

### 💰 智能费用分摊
- **科学算法**: 基于床位天数的精确费用分摊计算
- **多场景支持**: 支持有人入住、空宿舍、混合场景的费用分摊
- **实时计算**: 动态计算各部门费用分摊比例
- **历史追溯**: 完整的费用分摊历史记录

### 📊 统计报表
- **多维度统计**: 按月度、部门、宿舍等维度统计分析
- **可视化图表**: 直观的数据展示和趋势分析
- **报表导出**: 支持Excel、PDF格式报表导出
- **实时监控**: 实时床位使用情况监控

### 🔐 系统特性
- **用户认证**: JWT令牌认证，支持LDAP集成
- **权限控制**: 基于角色的访问控制(RBAC)
- **数据安全**: 完整的数据备份和恢复机制
- **日志审计**: 完整的操作日志和审计追踪

## 🏗️ 技术架构

### 后端技术栈
- **FastAPI**: 高性能异步Web框架，自动生成API文档
- **SQLAlchemy**: 强大的ORM框架，支持多种数据库
- **Pydantic**: 数据验证和序列化，类型安全
- **Alembic**: 数据库迁移管理
- **PostgreSQL/SQLite**: 关系型数据库存储
- **Redis**: 缓存和会话存储
- **Loguru**: 结构化日志管理

### 前端技术栈
- **Vue 3**: 渐进式JavaScript框架，Composition API
- **Element Plus**: 企业级UI组件库
- **Pinia**: 轻量级状态管理
- **Vue Router**: 单页面应用路由
- **Axios**: HTTP客户端，请求拦截
- **ECharts**: 数据可视化图表库
- **Vite**: 快速构建工具

## 📁 项目结构

```
BedSharingCalc/
├── app/                          # 🐍 后端应用
│   ├── api/                      # 🛣️ API路由层
│   │   ├── v1/                   # API版本1
│   │   │   ├── auth.py           # 认证接口
│   │   │   ├── departments.py    # 部门管理接口
│   │   │   ├── dormitories.py    # 宿舍管理接口
│   │   │   ├── residents.py      # 住户管理接口
│   │   │   ├── records.py        # 入住记录接口
│   │   │   └── reports.py        # 报表统计接口
│   │   └── deps.py               # 依赖注入
│   ├── core/                     # ⚙️ 核心配置
│   │   ├── config.py             # 应用配置
│   │   ├── database.py           # 数据库配置
│   │   ├── logging.py            # 日志配置
│   │   └── security.py           # 安全配置
│   ├── models/                   # 🗄️ 数据模型
│   │   ├── base.py               # 基础模型
│   │   ├── department.py         # 部门模型
│   │   ├── dormitory.py          # 宿舍模型
│   │   ├── resident.py           # 住户模型
│   │   ├── record.py             # 入住记录模型
│   │   └── report.py             # 报表模型
│   ├── schemas/                  # 📋 Pydantic模式
│   │   ├── department.py         # 部门数据模式
│   │   ├── dormitory.py          # 宿舍数据模式
│   │   ├── resident.py           # 住户数据模式
│   │   ├── record.py             # 记录数据模式
│   │   └── report.py             # 报表数据模式
│   ├── services/                 # 🔧 业务服务层
│   │   ├── department_service.py # 部门业务逻辑
│   │   ├── dormitory_service.py  # 宿舍业务逻辑
│   │   ├── resident_service.py   # 住户业务逻辑
│   │   ├── record_service.py     # 记录业务逻辑
│   │   └── report_service.py     # 报表业务逻辑
│   ├── repositories/             # 🗃️ 数据访问层
│   ├── utils/                    # 🛠️ 工具类
│   └── main.py                   # 🚀 应用入口
├── frontend/                     # 🎨 前端应用
│   ├── src/                      # 源代码
│   │   ├── components/           # Vue组件
│   │   ├── views/                # 页面视图
│   │   ├── stores/               # Pinia状态管理
│   │   ├── utils/                # 工具函数
│   │   ├── router/               # 路由配置
│   │   └── main.js               # 应用入口
│   ├── public/                   # 静态资源
│   ├── package.json              # 前端依赖
│   └── vite.config.js            # Vite配置
├── tests/                        # 🧪 测试代码
├── scripts/                      # 📜 脚本文件
├── 概要设计/                      # 📖 设计文档
├── docker-compose.yml            # 🐳 Docker编排
├── Dockerfile                    # 🐳 Docker镜像
├── requirements.txt              # 🐍 Python依赖
├── .env.example                  # 🔧 环境变量示例
└── README.md                     # 📚 项目说明
```

## 🚀 快速开始

### 📋 环境要求

- **Python**: 3.8+ (推荐3.11)
- **Node.js**: 16+ (推荐18+)
- **Docker**: 20.10+ (可选，用于容器化部署)
- **PostgreSQL**: 13+ (生产环境推荐)

### 🛠️ 开发环境安装

#### 方式一：传统安装

1. **克隆项目**
```bash
git clone <repository-url>
cd BedSharingCalc
```

2. **后端环境配置**
```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 安装Python依赖
pip install -r requirements.txt

# 配置环境变量
cp .env.example .env
# 编辑.env文件，修改相关配置
```

3. **前端环境配置**
```bash
cd frontend
npm install
# 或使用yarn
yarn install
```

4. **数据库初始化**
```bash
# 创建数据库表
python scripts/init_data.py
```

#### 方式二：Docker快速启动

```bash
# 启动所有服务（推荐）
docker-compose up -d

# 仅启动开发环境
docker-compose -f docker-compose.dev.yml up -d
```

### 🏃‍♂️ 运行应用

#### 开发模式

1. **启动后端服务**
```bash
# 开发模式启动
python run.py

# 或使用uvicorn直接启动
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

2. **启动前端服务**
```bash
cd frontend
npm run dev
# 或
yarn dev
```

3. **访问应用**
- 前端应用: http://localhost:3000
- 后端API: http://localhost:8000
- API文档: http://localhost:8000/docs
- ReDoc文档: http://localhost:8000/redoc

#### 生产模式

```bash
# 构建前端
cd frontend
npm run build

# 启动生产服务
docker-compose -f docker-compose.prod.yml up -d
```

## 📡 API接口文档

### 🔐 认证接口
```http
POST /api/v1/auth/login          # 用户登录
POST /api/v1/auth/logout         # 用户登出
POST /api/v1/auth/refresh        # 刷新令牌
GET  /api/v1/auth/me             # 获取当前用户信息
```

### 🏢 部门管理
```http
GET    /api/v1/departments/      # 获取部门列表
POST   /api/v1/departments/      # 创建部门
GET    /api/v1/departments/{id}  # 获取部门详情
PUT    /api/v1/departments/{id}  # 更新部门
DELETE /api/v1/departments/{id}  # 删除部门
```

### 🏠 宿舍管理
```http
GET    /api/v1/dormitories/      # 获取宿舍列表
POST   /api/v1/dormitories/      # 创建宿舍
GET    /api/v1/dormitories/{id}  # 获取宿舍详情
PUT    /api/v1/dormitories/{id}  # 更新宿舍
DELETE /api/v1/dormitories/{id}  # 删除宿舍
GET    /api/v1/dormitories/{id}/beds  # 获取床位状态
```

### 👥 住户管理
```http
GET    /api/v1/residents/        # 获取住户列表
POST   /api/v1/residents/        # 创建住户
GET    /api/v1/residents/{id}    # 获取住户详情
PUT    /api/v1/residents/{id}    # 更新住户
DELETE /api/v1/residents/{id}    # 删除住户
```

### 📝 入住记录
```http
GET    /api/v1/records/          # 获取入住记录列表
POST   /api/v1/records/          # 创建入住记录
GET    /api/v1/records/{id}      # 获取记录详情
PUT    /api/v1/records/{id}      # 更新记录（如离开）
DELETE /api/v1/records/{id}      # 删除记录
POST   /api/v1/records/{id}/checkout  # 办理离开
```

### 📊 报表统计
```http
GET    /api/v1/reports/monthly   # 获取月度报表
GET    /api/v1/reports/daily     # 获取日度分摊
GET    /api/v1/reports/export    # 导出报表
GET    /api/v1/reports/dashboard # 获取仪表盘数据
```

### 📋 API使用示例

#### 创建部门
```bash
curl -X POST "http://localhost:8000/api/v1/departments/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "name": "技术部",
    "description": "负责技术开发工作",
    "manager": "张三"
  }'
```

#### 创建入住记录
```bash
curl -X POST "http://localhost:8000/api/v1/records/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "resident_id": "resident-uuid",
    "dormitory_id": "dormitory-uuid",
    "bed_number": 1,
    "check_in_date": "2024-01-01",
    "notes": "正常入住"
  }'
```

## 🗄️ 数据库设计

### 核心数据表

| 表名 | 说明 | 主要字段 |
|------|------|----------|
| `departments` | 部门表 | id, name, description, manager |
| `dormitories` | 宿舍表 | id, name, total_beds, description |
| `residents` | 住户表 | id, name, employee_id, department_id |
| `residence_records` | 入住记录表 | id, resident_id, dormitory_id, check_in_date, check_out_date |
| `monthly_reports` | 月度报告表 | id, year, month, total_bed_days, department_summary |
| `daily_allocations` | 日度分摊表 | id, allocation_date, dormitory_id, department_allocations |

### 数据库关系图

```mermaid
erDiagram
    Department ||--o{ Resident : belongs_to
    Resident ||--o{ ResidenceRecord : has
    Dormitory ||--o{ ResidenceRecord : contains
    MonthlyReport ||--o{ DailyAllocation : includes
    Dormitory ||--o{ DailyAllocation : allocates

    Department {
        string id PK
        string name
        string description
        string manager
        datetime created_at
    }

    Dormitory {
        string id PK
        string name
        int total_beds
        string description
        boolean is_active
        datetime created_at
    }

    Resident {
        string id PK
        string name
        string employee_id
        string phone
        string email
        string department_id FK
        boolean is_active
        datetime created_at
    }

    ResidenceRecord {
        string id PK
        string resident_id FK
        string dormitory_id FK
        int bed_number
        date check_in_date
        date check_out_date
        string status
        string notes
        datetime created_at
    }
```

## 💰 费用分摊算法

### 核心算法逻辑

系统采用基于**床位天数**的科学分摊算法：

#### 1. 日度分摊计算
```python
def calculate_daily_allocation(date, dormitory_id):
    """计算某天某宿舍的部门分摊"""
    # 获取当天入住情况
    occupied_beds = get_occupied_beds(date, dormitory_id)

    if not occupied_beds:
        # 空宿舍：公司承担100%
        return {"公司": dormitory.total_beds}

    # 按部门统计床位数
    dept_beds = defaultdict(int)
    for bed in occupied_beds:
        dept_beds[bed.resident.department.name] += 1

    return dict(dept_beds)
```

#### 2. 月度汇总计算
```python
def calculate_monthly_summary(year, month):
    """计算月度各部门费用分摊"""
    total_allocations = defaultdict(float)

    for day in get_month_days(year, month):
        for dormitory in dormitories:
            daily_alloc = calculate_daily_allocation(day, dormitory.id)
            for dept, beds in daily_alloc.items():
                total_allocations[dept] += beds

    return total_allocations
```

### 分摊场景说明

| 场景 | 分摊规则 | 示例 |
|------|----------|------|
| **有人入住** | 按入住部门的床位数平均分摊 | 技术部2人，财务部1人 → 技术部66.7%，财务部33.3% |
| **空宿舍** | 公司承担100%费用 | 无人入住 → 公司100% |
| **混合场景** | 按实际床位占用比例分摊 | 5床位宿舍，3人入住 → 按3个床位分摊，2个空床位公司承担 |

### 费用计算公式

```
部门月度费用 = 部门床位天数 × 单床日费用
分摊比例 = 部门床位天数 ÷ 总床位天数 × 100%
```

## 🧪 测试

### 运行测试

```bash
# 运行所有测试
pytest

# 运行测试并生成覆盖率报告
pytest --cov=app --cov-report=html

# 运行特定测试文件
pytest tests/test_departments.py

# 运行特定测试函数
pytest tests/test_departments.py::test_create_department
```

### 测试覆盖率

当前测试覆盖率目标：**>90%**

- 单元测试：覆盖所有业务逻辑
- 集成测试：覆盖API接口
- 端到端测试：覆盖关键业务流程

## 🚀 部署

### Docker部署（推荐）

```bash
# 生产环境部署
docker-compose -f docker-compose.prod.yml up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f app
```

### 传统部署

```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 构建前端
cd frontend && npm run build

# 3. 配置环境变量
export DATABASE_URL="postgresql://user:pass@localhost/dbname"
export SECRET_KEY="your-secret-key"

# 4. 启动服务
gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker
```

### 环境变量配置

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| `DATABASE_URL` | 数据库连接URL | `sqlite:///./bedsharing.db` |
| `SECRET_KEY` | JWT密钥 | 必填 |
| `DEBUG` | 调试模式 | `False` |
| `LOG_LEVEL` | 日志级别 | `INFO` |
| `CORS_ORIGINS` | 跨域允许源 | `["*"]` |

## 📈 开发计划

### 已完成 ✅
- [x] 项目架构设计
- [x] 后端基础框架搭建
- [x] 数据模型设计
- [x] 部门管理API
- [x] 宿舍管理API
- [x] 住户管理API
- [x] 入住记录API
- [x] JWT认证系统

### 进行中 🚧
- [ ] 费用分摊算法优化
- [ ] 报表统计API完善
- [ ] 前端界面开发
- [ ] 单元测试补充

### 计划中 📋
- [ ] LDAP集成认证
- [ ] 数据导入导出功能
- [ ] 移动端适配
- [ ] 微信小程序
- [ ] 系统监控告警
- [ ] 多租户支持

## 🤝 贡献指南

### 开发流程

1. **Fork项目** 到你的GitHub账户
2. **创建特性分支** (`git checkout -b feature/AmazingFeature`)
3. **提交更改** (`git commit -m 'Add some AmazingFeature'`)
4. **推送到分支** (`git push origin feature/AmazingFeature`)
5. **创建Pull Request**

### 代码规范

- **Python**: 遵循PEP 8，使用Black格式化
- **JavaScript**: 遵循ESLint规则
- **提交信息**: 使用约定式提交格式
- **文档**: 更新相关文档和注释

### 问题反馈

- 🐛 **Bug报告**: 使用Issue模板报告问题
- 💡 **功能建议**: 提交Feature Request
- 📖 **文档改进**: 欢迎改进文档

## 📄 许可证

本项目采用 [MIT许可证](LICENSE) - 查看LICENSE文件了解详情。

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者！

## 📞 联系我们

- 📧 邮箱: [<EMAIL>](mailto:<EMAIL>)
- 🐛 问题反馈: [GitHub Issues](https://github.com/your-repo/issues)
- 📖 文档: [项目文档](https://your-docs-url.com)

---

<div align="center">
  <p>如果这个项目对你有帮助，请给它一个 ⭐️</p>
  <p>Made with ❤️ by the BedSharingCalc Team</p>
</div>
