#!/usr/bin/env python3
"""
数据库初始化脚本
"""
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)
os.chdir(project_root)

from sqlalchemy.orm import Session
from app.core.database import SessionLocal, create_tables
from app.models import Department, Dormitory, Resident
from app.core.logging import get_logger

logger = get_logger(__name__)


def init_departments(db: Session):
    """初始化部门数据"""
    departments_data = [
        {"name": "技术部", "description": "负责技术开发和维护", "is_active": True},
        {"name": "市场部", "description": "负责市场推广和销售", "is_active": True},
        {"name": "人事部", "description": "负责人力资源管理", "is_active": True},
        {"name": "财务部", "description": "负责财务管理和会计", "is_active": True},
        {"name": "运营部", "description": "负责日常运营管理", "is_active": True},
    ]
    
    for dept_data in departments_data:
        # 检查是否已存在
        existing = db.query(Department).filter(Department.name == dept_data["name"]).first()
        if not existing:
            department = Department(**dept_data)
            db.add(department)
            logger.info(f"创建部门: {dept_data['name']}")
    
    db.commit()


def init_dormitories(db: Session):
    """初始化宿舍数据"""
    dormitories_data = [
        {"name": "A栋101", "total_beds": 4, "description": "4人间宿舍", "is_active": True},
        {"name": "A栋102", "total_beds": 4, "description": "4人间宿舍", "is_active": True},
        {"name": "A栋103", "total_beds": 2, "description": "2人间宿舍", "is_active": True},
        {"name": "B栋201", "total_beds": 6, "description": "6人间宿舍", "is_active": True},
        {"name": "B栋202", "total_beds": 4, "description": "4人间宿舍", "is_active": True},
        {"name": "C栋301", "total_beds": 2, "description": "2人间宿舍", "is_active": True},
    ]
    
    for dorm_data in dormitories_data:
        # 检查是否已存在
        existing = db.query(Dormitory).filter(Dormitory.name == dorm_data["name"]).first()
        if not existing:
            dormitory = Dormitory(**dorm_data)
            db.add(dormitory)
            logger.info(f"创建宿舍: {dorm_data['name']}")
    
    db.commit()


def init_residents(db: Session):
    """初始化住户数据"""
    # 获取部门ID
    departments = db.query(Department).all()
    if not departments:
        logger.error("请先初始化部门数据")
        return
    
    dept_map = {dept.name: dept.id for dept in departments}
    
    residents_data = [
        {"name": "张三", "employee_id": "EMP001", "phone": "13800138001", 
         "email": "<EMAIL>", "department_id": dept_map.get("技术部")},
        {"name": "李四", "employee_id": "EMP002", "phone": "13800138002", 
         "email": "<EMAIL>", "department_id": dept_map.get("技术部")},
        {"name": "王五", "employee_id": "EMP003", "phone": "13800138003", 
         "email": "<EMAIL>", "department_id": dept_map.get("市场部")},
        {"name": "赵六", "employee_id": "EMP004", "phone": "13800138004", 
         "email": "<EMAIL>", "department_id": dept_map.get("市场部")},
        {"name": "钱七", "employee_id": "EMP005", "phone": "13800138005", 
         "email": "<EMAIL>", "department_id": dept_map.get("人事部")},
        {"name": "孙八", "employee_id": "EMP006", "phone": "13800138006", 
         "email": "<EMAIL>", "department_id": dept_map.get("财务部")},
    ]
    
    for resident_data in residents_data:
        if resident_data["department_id"]:  # 确保部门ID存在
            # 检查是否已存在
            existing = db.query(Resident).filter(
                Resident.employee_id == resident_data["employee_id"]
            ).first()
            if not existing:
                resident = Resident(**resident_data)
                db.add(resident)
                logger.info(f"创建住户: {resident_data['name']}")
    
    db.commit()


def main():
    """主函数"""
    logger.info("开始初始化数据库...")
    
    # 创建数据库表
    create_tables()
    logger.info("数据库表创建完成")
    
    # 获取数据库会话
    db = SessionLocal()
    
    try:
        # 初始化基础数据
        init_departments(db)
        init_dormitories(db)
        init_residents(db)
        
        logger.info("数据库初始化完成！")
        
    except Exception as e:
        logger.error(f"数据库初始化失败: {str(e)}")
        db.rollback()
        raise
    finally:
        db.close()


if __name__ == "__main__":
    main()
