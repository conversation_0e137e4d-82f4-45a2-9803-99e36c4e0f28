-- 数据库初始化脚本
-- 创建必要的扩展和初始数据

-- 创建UUID扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 创建全文搜索扩展
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- 设置时区
SET timezone = 'Asia/Shanghai';

-- 创建初始管理员用户（如果需要）
-- 注意：实际密码应该通过应用程序设置

-- 插入默认部门数据
-- 这些数据将在应用启动时通过Python脚本插入

-- 创建索引优化查询性能
-- 这些索引将在SQLAlchemy模型中定义

-- 设置数据库参数优化性能
ALTER SYSTEM SET shared_preload_libraries = 'pg_stat_statements';
ALTER SYSTEM SET track_activity_query_size = 2048;
ALTER SYSTEM SET pg_stat_statements.track = 'all';

-- 重新加载配置
SELECT pg_reload_conf();
