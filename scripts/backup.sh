#!/bin/bash

# 数据库备份脚本
# 用于定期备份PostgreSQL数据库

set -e

# 配置变量
BACKUP_DIR="/backups"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="dormitory_db_backup_${DATE}.sql"
RETENTION_DAYS=${BACKUP_RETENTION_DAYS:-30}

# 数据库连接信息
DB_HOST="db"
DB_PORT="5432"
DB_NAME=${POSTGRES_DB:-"dormitory_db"}
DB_USER=${POSTGRES_USER:-"postgres"}

# 创建备份目录
mkdir -p ${BACKUP_DIR}

echo "开始备份数据库: ${DB_NAME}"
echo "备份时间: $(date)"

# 执行备份
PGPASSWORD=${POSTGRES_PASSWORD} pg_dump \
    -h ${DB_HOST} \
    -p ${DB_PORT} \
    -U ${DB_USER} \
    -d ${DB_NAME} \
    --verbose \
    --clean \
    --no-owner \
    --no-privileges \
    --format=custom \
    --file=${BACKUP_DIR}/${BACKUP_FILE}

# 检查备份是否成功
if [ $? -eq 0 ]; then
    echo "数据库备份成功: ${BACKUP_FILE}"
    
    # 压缩备份文件
    gzip ${BACKUP_DIR}/${BACKUP_FILE}
    echo "备份文件已压缩: ${BACKUP_FILE}.gz"
    
    # 清理旧备份文件
    echo "清理 ${RETENTION_DAYS} 天前的备份文件..."
    find ${BACKUP_DIR} -name "dormitory_db_backup_*.sql.gz" -mtime +${RETENTION_DAYS} -delete
    
    echo "备份完成!"
else
    echo "数据库备份失败!"
    exit 1
fi

# 显示备份文件信息
echo "当前备份文件列表:"
ls -lh ${BACKUP_DIR}/dormitory_db_backup_*.sql.gz 2>/dev/null || echo "没有找到备份文件"
