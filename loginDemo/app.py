from flask import Flask, render_template, request
from ldap3 import Server, Connection, ALL, core
import logging

app = Flask(__name__)

LDAP_URI = 'ldap://192.168.10.113:389'
LDAP_BASE_DN_USERS = 'dc=users,dc=appdata,dc=erayt,dc=com'

logger = logging.getLogger(__name__)

def get_user_dn(username: str, basedn: str) -> str:
    # 按你的 ldapsearch 方式拼接
    return f"cn={username},{basedn}"

def valid_user(ldap_uri: str, basedn: str, username: str, password: str) -> bool:
    user_dn = get_user_dn(username, basedn)
    print(f"尝试绑定的DN: {user_dn}")
    server = Server(ldap_uri, get_info=ALL, connect_timeout=3)
    try:
        conn = Connection(
            server,
            user=user_dn,
            password=password,
            authentication='SIMPLE',
            auto_bind=True,
        )
        conn.unbind()
        logger.info(f"[LDAP用户认证成功]：{username}")
        return True
    except core.exceptions.LDAPBindError as e:
        logger.error(f"[LDAP用户认证失败]：{username}, {e}")
        return False
    except Exception as e:
        logger.error(f"[LDAP服务连接失败]：{username}, {e}")
        return False

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username', '').strip()
        password = request.form.get('password', '')
        success = valid_user(LDAP_URI, LDAP_BASE_DN_USERS, username, password)
        if success:
            message = f"用户 {username} 登录成功"
        else:
            message = "用户名或密码错误，或LDAP服务不可用"
        return render_template('result.html', success=success, message=message)
    return render_template('login.html')

if __name__ == '__main__':
    app.run(debug=True)