version: '3.8'

services:
  # 后端开发服务
  backend-dev:
    build: 
      context: .
      dockerfile: Dockerfile.dev
    container_name: bedsharing-backend-dev
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=**************************************/dormitory_db
      - SECRET_KEY=dev-secret-key-not-for-production
      - DEBUG=true
      - LOG_LEVEL=DEBUG
      - CORS_ORIGINS=["http://localhost:3000","http://localhost:8080"]
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - .:/app
      - /app/__pycache__
      - ./logs:/app/logs
    restart: unless-stopped
    networks:
      - bedsharing-network
    command: uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

  # 前端开发服务
  frontend-dev:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: bedsharing-frontend-dev
    ports:
      - "3000:3000"
    environment:
      - VITE_API_BASE_URL=http://localhost:8000/api/v1
      - VITE_APP_TITLE=宿舍入住管理系统（开发环境）
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend-dev
    restart: unless-stopped
    networks:
      - bedsharing-network
    command: npm run dev

  # PostgreSQL数据库
  db:
    image: postgres:15-alpine
    container_name: bedsharing-db-dev
    environment:
      - POSTGRES_DB=dormitory_db
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    ports:
      - "5432:5432"
    restart: unless-stopped
    networks:
      - bedsharing-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d dormitory_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: bedsharing-redis-dev
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data
    restart: unless-stopped
    networks:
      - bedsharing-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # 数据库管理工具
  adminer:
    image: adminer:latest
    container_name: bedsharing-adminer-dev
    ports:
      - "8080:8080"
    depends_on:
      - db
    restart: unless-stopped
    networks:
      - bedsharing-network

volumes:
  postgres_dev_data:
    driver: local
  redis_dev_data:
    driver: local

networks:
  bedsharing-network:
    driver: bridge
