version: '3.8'

services:
  # 后端API服务
  backend:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: bedsharing-backend
    ports:
      - "${BACKEND_PORT:-8000}:8000"
    environment:
      - DATABASE_URL=postgresql://${POSTGRES_USER:-postgres}:${POSTGRES_PASSWORD:-password}@db:5432/${POSTGRES_DB:-dormitory_db}
      - SECRET_KEY=${SECRET_KEY:-your-secret-key-change-in-production}
      - DEBUG=${DEBUG:-false}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - CORS_ORIGINS=["http://localhost:3000","http://localhost:8080","http://frontend:3000"]
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/uploads
    restart: unless-stopped
    networks:
      - bedsharing-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # 前端服务（开发环境）
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: bedsharing-frontend
    ports:
      - "${FRONTEND_PORT:-3000}:3000"
    environment:
      - VITE_API_BASE_URL=http://localhost:${BACKEND_PORT:-8000}/api/v1
      - VITE_APP_TITLE=宿舍入住管理系统
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - bedsharing-network
    profiles:
      - dev

  # PostgreSQL数据库
  db:
    image: postgres:15-alpine
    container_name: bedsharing-db
    environment:
      - POSTGRES_DB=${POSTGRES_DB:-dormitory_db}
      - POSTGRES_USER=${POSTGRES_USER:-postgres}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-password}
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    restart: unless-stopped
    networks:
      - bedsharing-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-postgres} -d ${POSTGRES_DB:-dormitory_db}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: bedsharing-redis
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-}
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - bedsharing-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s

  # Nginx反向代理（生产环境）
  nginx:
    image: nginx:alpine
    container_name: bedsharing-nginx
    ports:
      - "${HTTP_PORT:-80}:80"
      - "${HTTPS_PORT:-443}:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - bedsharing-network
    profiles:
      - prod

  # 数据库管理工具（可选）
  adminer:
    image: adminer:latest
    container_name: bedsharing-adminer
    ports:
      - "${ADMINER_PORT:-8080}:8080"
    depends_on:
      - db
    restart: unless-stopped
    networks:
      - bedsharing-network
    profiles:
      - dev
      - tools

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  nginx_logs:
    driver: local

networks:
  bedsharing-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
