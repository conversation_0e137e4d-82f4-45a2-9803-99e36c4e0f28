# 🚀 部署指南

本文档详细说明了宿舍入住管理系统的部署方法。

## 📋 环境要求

### 基础环境
- **Docker**: 20.10+ 
- **Docker Compose**: 2.0+
- **Git**: 用于克隆代码

### 开发环境额外要求
- **Python**: 3.8+ (推荐3.11)
- **Node.js**: 16+ (推荐18+)
- **npm**: 8+ 或 **yarn**: 1.22+

## 🛠️ 快速部署

### 1. 克隆项目

```bash
git clone <repository-url>
cd BedSharingCalc
```

### 2. 环境配置

```bash
# 复制环境变量文件
cp .env.example .env

# 编辑环境变量（重要！）
# 修改数据库密码、JWT密钥等敏感信息
vim .env  # 或使用其他编辑器
```

### 3. 选择部署方式

#### 方式一：开发环境（推荐用于开发和测试）

```bash
# 启动开发环境
docker-compose -f docker-compose.dev.yml up -d

# 查看服务状态
docker-compose -f docker-compose.dev.yml ps

# 查看日志
docker-compose -f docker-compose.dev.yml logs -f
```

访问地址：
- 前端应用: http://localhost:3000
- 后端API: http://localhost:8000
- API文档: http://localhost:8000/docs
- 数据库管理: http://localhost:8080

#### 方式二：生产环境

```bash
# 启动生产环境
docker-compose -f docker-compose.prod.yml up -d

# 查看服务状态
docker-compose -f docker-compose.prod.yml ps
```

访问地址：
- 应用: http://localhost (通过Nginx代理)
- HTTPS: https://localhost (需要配置SSL证书)

## 🔧 详细配置

### 环境变量说明

| 变量名 | 说明 | 默认值 | 必填 |
|--------|------|--------|------|
| `SECRET_KEY` | JWT密钥 | - | ✅ |
| `POSTGRES_PASSWORD` | 数据库密码 | password | ✅ |
| `DATABASE_URL` | 数据库连接URL | - | ✅ |
| `DEBUG` | 调试模式 | false | ❌ |
| `LOG_LEVEL` | 日志级别 | INFO | ❌ |

### SSL证书配置（生产环境）

1. 将SSL证书文件放置在 `nginx/ssl/` 目录：
   ```
   nginx/ssl/
   ├── cert.pem    # 证书文件
   └── key.pem     # 私钥文件
   ```

2. 修改 `nginx/nginx.prod.conf` 中的域名配置

3. 更新 `.env` 文件中的 `DOMAIN_NAME`

### 数据库初始化

```bash
# 方法1：使用Docker Compose
docker-compose exec backend python scripts/init_data.py

# 方法2：使用Makefile
make db-init
```

## 📊 监控和维护

### 查看服务状态

```bash
# 查看所有容器状态
docker-compose ps

# 查看特定服务日志
docker-compose logs -f backend
docker-compose logs -f frontend
docker-compose logs -f db
```

### 数据库备份

```bash
# 手动备份
make db-backup

# 或使用Docker命令
docker-compose exec db pg_dump -U postgres dormitory_db > backup.sql
```

### 更新应用

```bash
# 拉取最新代码
git pull

# 重新构建并启动
docker-compose -f docker-compose.prod.yml up -d --build
```

## 🐛 故障排除

### 常见问题

1. **端口冲突**
   ```bash
   # 检查端口占用
   netstat -tulpn | grep :8000
   
   # 修改 .env 文件中的端口配置
   BACKEND_PORT=8001
   ```

2. **数据库连接失败**
   ```bash
   # 检查数据库容器状态
   docker-compose logs db
   
   # 重启数据库服务
   docker-compose restart db
   ```

3. **前端构建失败**
   ```bash
   # 清理前端依赖
   cd frontend
   rm -rf node_modules package-lock.json
   npm install
   ```

4. **权限问题**
   ```bash
   # 修复文件权限
   sudo chown -R $USER:$USER .
   chmod +x scripts/*.sh
   ```

### 日志查看

```bash
# 查看应用日志
docker-compose logs -f --tail=100 backend

# 查看Nginx日志
docker-compose logs -f nginx

# 查看数据库日志
docker-compose logs -f db
```

## 🔒 安全建议

### 生产环境安全配置

1. **修改默认密码**
   - 数据库密码
   - Redis密码（如果启用）
   - 管理员账户密码

2. **配置防火墙**
   ```bash
   # 只开放必要端口
   ufw allow 80/tcp
   ufw allow 443/tcp
   ufw enable
   ```

3. **启用HTTPS**
   - 配置SSL证书
   - 强制HTTPS重定向
   - 设置安全头

4. **定期备份**
   - 设置自动备份计划
   - 测试备份恢复流程

### 监控配置

```bash
# 启用监控服务（可选）
docker-compose --profile monitoring -f docker-compose.prod.yml up -d

# 访问监控面板
# Prometheus: http://localhost:9090
# Grafana: http://localhost:3001
```

## 📞 技术支持

如果遇到问题，请：

1. 查看日志文件
2. 检查环境变量配置
3. 参考故障排除部分
4. 提交Issue到项目仓库

---

**注意**: 生产环境部署前，请务必修改所有默认密码和密钥！
