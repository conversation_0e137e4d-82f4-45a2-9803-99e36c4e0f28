"""
JWT处理模块
"""
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from jose import JWTError, jwt
from app.core.config import settings
from app.core.logging import get_logger

logger = get_logger(__name__)


def create_access_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
    """
    创建JWT访问令牌
    
    Args:
        data: 要编码的数据
        expires_delta: 过期时间增量
        
    Returns:
        str: JWT令牌
    """
    to_encode = data.copy()
    
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=settings.access_token_expire_minutes)
    
    to_encode.update({
        "exp": expire,
        "iat": datetime.utcnow(),
        "type": "access"
    })
    
    try:
        encoded_jwt = jwt.encode(to_encode, settings.secret_key, algorithm=settings.algorithm)
        logger.info(f"JWT令牌创建成功，用户: {data.get('sub', 'unknown')}")
        return encoded_jwt
    except Exception as e:
        logger.error(f"JWT令牌创建失败: {e}")
        raise


def verify_token(token: str) -> Optional[Dict[str, Any]]:
    """
    验证JWT令牌
    
    Args:
        token: JWT令牌
        
    Returns:
        Optional[Dict[str, Any]]: 解码后的载荷，验证失败返回None
    """
    try:
        payload = jwt.decode(
            token, 
            settings.secret_key, 
            algorithms=[settings.algorithm]
        )
        
        # 验证令牌类型
        if payload.get("type") != "access":
            logger.warning("无效的令牌类型")
            return None
            
        # 验证必要字段
        if not payload.get("sub"):
            logger.warning("令牌缺少用户标识")
            return None
            
        logger.debug(f"JWT令牌验证成功，用户: {payload.get('sub')}")
        return payload
        
    except jwt.ExpiredSignatureError:
        logger.warning("JWT令牌已过期")
        return None
        
    except JWTError as e:
        logger.error(f"JWT令牌验证失败: {e}")
        return None
        
    except Exception as e:
        logger.error(f"JWT令牌验证异常: {e}")
        return None


def get_token_expire_time() -> int:
    """
    获取令牌过期时间（秒）
    
    Returns:
        int: 过期时间（秒）
    """
    return settings.access_token_expire_minutes * 60


def create_user_token(username: str) -> str:
    """
    为用户创建访问令牌
    
    Args:
        username: 用户名
        
    Returns:
        str: JWT令牌
    """
    token_data = {
        "sub": username,
        "username": username
    }
    return create_access_token(token_data)
