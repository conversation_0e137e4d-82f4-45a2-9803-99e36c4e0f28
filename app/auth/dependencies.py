"""
认证依赖注入模块
"""
from typing import Optional
from fastapi import Depends, HTTPException, status
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from app.auth.jwt_handler import verify_token
from app.core.logging import get_logger

logger = get_logger(__name__)

# HTTP Bearer认证方案
security = HTTPBearer(auto_error=False)


def get_current_user(credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)) -> str:
    """
    获取当前用户信息
    
    Args:
        credentials: HTTP认证凭据
        
    Returns:
        str: 用户名
        
    Raises:
        HTTPException: 认证失败时抛出401异常
    """
    if not credentials:
        logger.warning("请求缺少认证令牌")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="缺少认证令牌",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    token = credentials.credentials
    payload = verify_token(token)
    
    if payload is None:
        logger.warning("无效的认证令牌")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的认证令牌",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    username: str = payload.get("sub")
    if username is None:
        logger.warning("令牌中缺少用户标识")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的认证令牌",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    logger.debug(f"用户认证成功: {username}")
    return username


def get_optional_current_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
) -> Optional[str]:
    """
    获取当前用户信息（可选）
    
    Args:
        credentials: HTTP认证凭据
        
    Returns:
        Optional[str]: 用户名，认证失败返回None
    """
    if not credentials:
        return None
    
    token = credentials.credentials
    payload = verify_token(token)
    
    if payload is None:
        return None
    
    username: str = payload.get("sub")
    return username


class AuthRequired:
    """认证装饰器类"""
    
    def __init__(self, required: bool = True):
        self.required = required
    
    def __call__(self, credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)) -> Optional[str]:
        if self.required:
            return get_current_user(credentials)
        else:
            return get_optional_current_user(credentials)


# 常用的依赖实例
auth_required = AuthRequired(required=True)
auth_optional = AuthRequired(required=False)
