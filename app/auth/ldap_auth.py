"""
LDAP认证模块
"""
from ldap3 import Server, Connection, ALL, core
from app.core.config import settings
from app.core.logging import get_logger

logger = get_logger(__name__)


def get_user_dn(username: str) -> str:
    """构建用户DN"""
    return f"cn={username},{settings.ldap_base_dn_users}"


def authenticate_user(username: str, password: str) -> bool:
    """
    LDAP用户认证

    Args:
        username: 用户名
        password: 密码

    Returns:
        bool: 认证是否成功
    """
    if not username or not password:
        logger.warning("用户名或密码为空")
        return False

    # 开发环境下的模拟认证（用于测试）
    # 在生产环境中应该移除这部分代码
    if username == "testuser" and password == "testpass":
        logger.info(f"模拟认证成功: {username}")
        return True

    if username == "admin" and password == "admin123":
        logger.info(f"模拟认证成功: {username}")
        return True

    user_dn = get_user_dn(username)
    logger.info(f"尝试LDAP认证，用户DN: {user_dn}")

    server = Server(
        settings.ldap_uri,
        get_info=ALL,
        connect_timeout=settings.ldap_connect_timeout
    )

    try:
        conn = Connection(
            server,
            user=user_dn,
            password=password,
            authentication='SIMPLE',
            auto_bind=True,
        )
        conn.unbind()
        logger.info(f"LDAP用户认证成功: {username}")
        return True

    except core.exceptions.LDAPBindError as e:
        logger.error(f"LDAP用户认证失败: {username}, 错误: {e}")
        return False

    except Exception as e:
        logger.error(f"LDAP服务连接失败: {username}, 错误: {e}")
        # 在LDAP服务不可用时，仍然允许模拟用户登录（仅用于开发测试）
        if username in ["testuser", "admin"]:
            logger.warning(f"LDAP服务不可用，使用模拟认证: {username}")
            return password in ["testpass", "admin123"]
        return False


def validate_username(username: str) -> bool:
    """
    验证用户名格式
    
    Args:
        username: 用户名
        
    Returns:
        bool: 用户名格式是否有效
    """
    if not username:
        return False
        
    # 基本格式验证：只允许字母、数字、下划线、点号
    import re
    pattern = r'^[a-zA-Z0-9._-]+$'
    return bool(re.match(pattern, username)) and len(username) <= 50
