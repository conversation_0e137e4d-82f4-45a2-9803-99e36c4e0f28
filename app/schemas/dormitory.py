"""
宿舍相关模式
"""
from typing import Optional, List, Dict
from datetime import datetime
from pydantic import BaseModel, Field, ConfigDict
from .base import BaseSchema


class DormitoryBase(BaseModel):
    """宿舍基础模式"""
    name: str = Field(..., min_length=1, max_length=100, description="宿舍名称")
    total_beds: int = Field(..., gt=0, description="床位总数")
    description: Optional[str] = Field(None, max_length=500, description="宿舍描述")
    department_id: Optional[str] = Field(None, description="所属部门ID")
    is_active: bool = Field(True, description="是否启用")


class DormitoryCreate(DormitoryBase):
    """创建宿舍模式"""
    pass


class DormitoryUpdate(BaseModel):
    """更新宿舍模式"""
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="宿舍名称")
    total_beds: Optional[int] = Field(None, gt=0, description="床位总数")
    description: Optional[str] = Field(None, max_length=500, description="宿舍描述")
    department_id: Optional[str] = Field(None, description="所属部门ID")
    is_active: Optional[bool] = Field(None, description="是否启用")


class DormitoryResponse(BaseSchema):
    """宿舍响应模式"""
    id: str = Field(..., description="宿舍ID")
    name: str = Field(..., description="宿舍名称")
    total_beds: int = Field(..., gt=0, description="总床位数")
    description: Optional[str] = Field(None, description="宿舍描述")
    department_id: Optional[str] = Field(None, description="所属部门ID")
    department_name: Optional[str] = Field(None, description="所属部门名称")
    is_active: bool = Field(True, description="是否启用")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    occupied_beds: Optional[int] = Field(None, description="已占用床位数")
    available_beds: Optional[int] = Field(None, description="可用床位数")


class BedInfo(BaseModel):
    """床位信息"""
    bed_number: int = Field(..., description="床位号")
    is_occupied: bool = Field(..., description="是否被占用")
    resident_name: Optional[str] = Field(None, description="住户姓名")
    resident_id: Optional[str] = Field(None, description="住户ID")
    check_in_date: Optional[str] = Field(None, description="入住日期")


class DormitoryBedStatus(BaseModel):
    """宿舍床位状态"""
    dormitory_id: str = Field(..., description="宿舍ID")
    dormitory_name: str = Field(..., description="宿舍名称")
    total_beds: int = Field(..., description="总床位数")
    occupied_beds: int = Field(..., description="已占用床位数")
    available_beds: int = Field(..., description="可用床位数")
    bed_details: List[BedInfo] = Field(..., description="床位详情列表")
