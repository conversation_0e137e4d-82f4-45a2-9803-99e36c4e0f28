"""
部门相关模式
"""
from typing import Optional
from datetime import datetime
from pydantic import BaseModel, Field, ConfigDict
from .base import BaseSchema


class DepartmentBase(BaseModel):
    """部门基础模式"""
    name: str = Field(..., min_length=1, max_length=100, description="部门名称")
    description: Optional[str] = Field(None, max_length=500, description="部门描述")
    is_active: bool = Field(True, description="是否启用")


class DepartmentCreate(DepartmentBase):
    """创建部门模式"""
    pass


class DepartmentUpdate(BaseModel):
    """更新部门模式"""
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="部门名称")
    description: Optional[str] = Field(None, max_length=500, description="部门描述")
    is_active: Optional[bool] = Field(None, description="是否启用")


class DepartmentResponse(BaseSchema):
    """部门响应模式"""
    id: str = Field(..., description="部门ID")
    name: str = Field(..., description="部门名称")
    description: Optional[str] = Field(None, description="部门描述")
    is_active: bool = Field(True, description="是否启用")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    resident_count: Optional[int] = Field(None, description="住户数量")
