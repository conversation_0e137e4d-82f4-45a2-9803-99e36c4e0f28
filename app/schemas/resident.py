"""
住户相关模式
"""
from typing import Optional

from pydantic import BaseModel, Field, EmailStr

from .base import TimestampMixin, IDMixin


class ResidentBase(BaseModel):
    """住户基础模式"""
    name: str = Field(..., min_length=1, max_length=100, description="姓名")
    employee_id: Optional[str] = Field(None, max_length=50, description="员工号")
    phone: Optional[str] = Field(None, max_length=20, description="电话号码")
    email: Optional[str] = Field(None, description="邮箱地址")
    department_id: str = Field(..., description="部门ID")
    is_active: bool = Field(True, description="是否启用")


class ResidentCreate(ResidentBase):
    """创建住户模式"""
    pass


class ResidentUpdate(BaseModel):
    """更新住户模式"""
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="姓名")
    employee_id: Optional[str] = Field(None, max_length=50, description="员工号")
    phone: Optional[str] = Field(None, max_length=20, description="电话号码")
    email: Optional[str] = Field(None, description="邮箱地址")
    department_id: Optional[str] = Field(None, description="部门ID")
    is_active: Optional[bool] = Field(None, description="是否启用")


class ResidentResponse(ResidentBase, IDMixin, TimestampMixin):
    """住户响应模式"""
    department_name: Optional[str] = Field(None, description="部门名称")
    current_dormitory: Optional[str] = Field(None, description="当前宿舍")
    current_bed_number: Optional[int] = Field(None, description="当前床位号")
