"""
基础模式类
"""
from typing import Optional
from datetime import datetime
from pydantic import BaseModel, ConfigDict


class BaseSchema(BaseModel):
    """基础模式类"""
    model_config = ConfigDict(
        from_attributes=True,
        arbitrary_types_allowed=True,
        use_enum_values=True
    )


class TimestampMixin:
    """时间戳混入类 - 使用普通类而不是BaseModel"""
    created_at: datetime
    updated_at: datetime


class IDMixin:
    """ID混入类 - 使用普通类而不是BaseModel"""
    id: str
