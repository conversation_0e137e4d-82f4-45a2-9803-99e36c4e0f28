"""
认证相关数据模型
"""
from pydantic import BaseModel, Field, validator
from typing import Optional
from datetime import datetime


class LoginRequest(BaseModel):
    """登录请求模型"""
    username: str = Field(..., description="用户名", min_length=1, max_length=50)
    password: str = Field(..., description="密码", min_length=1)
    
    @validator('username')
    def validate_username(cls, v):
        """验证用户名格式"""
        if not v or not v.strip():
            raise ValueError('用户名不能为空')
        
        # 移除前后空格
        v = v.strip()
        
        # 基本格式验证
        import re
        pattern = r'^[a-zA-Z0-9._-]+$'
        if not re.match(pattern, v):
            raise ValueError('用户名只能包含字母、数字、下划线、点号和连字符')
        
        return v
    
    @validator('password')
    def validate_password(cls, v):
        """验证密码"""
        if not v:
            raise ValueError('密码不能为空')
        return v


class UserInfo(BaseModel):
    """用户信息模型"""
    username: str = Field(..., description="用户名")
    login_time: Optional[datetime] = Field(None, description="登录时间")


class LoginResponse(BaseModel):
    """登录响应模型"""
    access_token: str = Field(..., description="访问令牌")
    token_type: str = Field(default="bearer", description="令牌类型")
    expires_in: int = Field(..., description="过期时间(秒)")
    user_info: UserInfo = Field(..., description="用户信息")


class TokenPayload(BaseModel):
    """JWT载荷模型"""
    sub: str = Field(..., description="用户标识")
    username: str = Field(..., description="用户名")
    exp: int = Field(..., description="过期时间戳")
    iat: int = Field(..., description="签发时间戳")
    type: str = Field(default="access", description="令牌类型")


class AuthResponse(BaseModel):
    """通用认证响应模型"""
    success: bool = Field(..., description="操作是否成功")
    message: str = Field(..., description="响应消息")
    data: Optional[dict] = Field(None, description="响应数据")


class UserProfile(BaseModel):
    """用户档案模型"""
    username: str = Field(..., description="用户名")
    is_authenticated: bool = Field(True, description="是否已认证")
    login_time: Optional[datetime] = Field(None, description="登录时间")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }
