"""
宿舍管理API
"""
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.api.deps import get_db
from app.schemas.dormitory import (
    DormitoryCreate, DormitoryUpdate, DormitoryResponse, DormitoryBedStatus
)
from app.services.dormitory_service import DormitoryService
from app.auth.dependencies import get_current_user

router = APIRouter()


@router.get("/", response_model=List[DormitoryResponse], summary="获取宿舍列表")
async def get_dormitories(
    skip: int = 0,
    limit: int = 100,
    is_active: bool = None,
    db: Session = Depends(get_db),
    current_user: str = Depends(get_current_user)
):
    """
    获取宿舍列表
    
    - **skip**: 跳过记录数
    - **limit**: 限制记录数
    - **is_active**: 是否启用筛选
    """
    service = DormitoryService(db)
    return await service.get_dormitories(skip=skip, limit=limit, is_active=is_active)


@router.post("/", response_model=DormitoryResponse, status_code=status.HTTP_201_CREATED, summary="创建宿舍")
async def create_dormitory(
    dormitory: DormitoryCreate,
    db: Session = Depends(get_db),
    current_user: str = Depends(get_current_user)
):
    """创建新宿舍"""
    service = DormitoryService(db)
    return await service.create_dormitory(dormitory)


@router.get("/{dormitory_id}", response_model=DormitoryResponse, summary="获取宿舍详情")
async def get_dormitory(
    dormitory_id: str,
    db: Session = Depends(get_db),
    current_user: str = Depends(get_current_user)
):
    """根据ID获取宿舍详情"""
    service = DormitoryService(db)
    dormitory = await service.get_dormitory_by_id(dormitory_id)
    if not dormitory:
        raise HTTPException(status_code=404, detail="宿舍不存在")
    return dormitory


@router.put("/{dormitory_id}", response_model=DormitoryResponse, summary="更新宿舍")
async def update_dormitory(
    dormitory_id: str,
    dormitory: DormitoryUpdate,
    db: Session = Depends(get_db),
    current_user: str = Depends(get_current_user)
):
    """更新宿舍信息"""
    service = DormitoryService(db)
    updated_dormitory = await service.update_dormitory(dormitory_id, dormitory)
    if not updated_dormitory:
        raise HTTPException(status_code=404, detail="宿舍不存在")
    return updated_dormitory


@router.delete("/{dormitory_id}", status_code=status.HTTP_204_NO_CONTENT, summary="删除宿舍")
async def delete_dormitory(
    dormitory_id: str,
    db: Session = Depends(get_db),
    current_user: str = Depends(get_current_user)
):
    """删除宿舍"""
    service = DormitoryService(db)
    success = await service.delete_dormitory(dormitory_id)
    if not success:
        raise HTTPException(status_code=404, detail="宿舍不存在")


@router.get("/{dormitory_id}/bed-status", response_model=DormitoryBedStatus, summary="获取宿舍床位状态")
async def get_dormitory_bed_status(
    dormitory_id: str,
    date: str = None,  # YYYY-MM-DD格式，默认为今天
    db: Session = Depends(get_db),
    current_user: str = Depends(get_current_user)
):
    """
    获取指定日期的宿舍床位状态
    
    - **dormitory_id**: 宿舍ID
    - **date**: 查询日期，格式为YYYY-MM-DD，不指定则为今天
    """
    service = DormitoryService(db)
    return await service.get_bed_status(dormitory_id, date)
