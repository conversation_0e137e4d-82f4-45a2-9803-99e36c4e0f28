"""
API v1 路由
"""
from fastapi import APIRouter
from .departments import router as departments_router
from .dormitories import router as dormitories_router
from .residents import router as residents_router
from .records import router as records_router
from .reports import router as reports_router

api_router = APIRouter()

# 注册子路由
api_router.include_router(departments_router, prefix="/departments", tags=["departments"])
api_router.include_router(dormitories_router, prefix="/dormitories", tags=["dormitories"])
api_router.include_router(residents_router, prefix="/residents", tags=["residents"])
api_router.include_router(records_router, prefix="/records", tags=["records"])
api_router.include_router(reports_router, prefix="/reports", tags=["reports"])
