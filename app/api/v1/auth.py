"""
认证API路由
"""
from datetime import datetime
from fastapi import APIRouter, HTTPException, status, Depends
from fastapi.security import HTTPAuthorizationCredentials
from app.schemas.auth import LoginRequest, LoginResponse, UserInfo, UserProfile, AuthResponse
from app.auth.ldap_auth import authenticate_user, validate_username
from app.auth.jwt_handler import create_user_token, get_token_expire_time
from app.auth.dependencies import get_current_user, security
from app.core.logging import get_logger

logger = get_logger(__name__)

router = APIRouter()


@router.post("/login", response_model=LoginResponse, summary="用户登录")
async def login(login_data: LoginRequest):
    """
    用户登录接口
    
    - **username**: 用户名
    - **password**: 密码
    
    返回JWT访问令牌和用户信息
    """
    username = login_data.username.strip()
    password = login_data.password
    
    logger.info(f"用户登录请求: {username}")
    
    # 验证用户名格式
    if not validate_username(username):
        logger.warning(f"无效的用户名格式: {username}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户名格式无效"
        )
    
    # LDAP认证
    try:
        is_authenticated = authenticate_user(username, password)
        if not is_authenticated:
            logger.warning(f"用户认证失败: {username}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误"
            )
    except Exception as e:
        logger.error(f"认证过程异常: {username}, 错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="认证服务暂时不可用，请稍后重试"
        )
    
    # 生成JWT令牌
    try:
        access_token = create_user_token(username)
        expires_in = get_token_expire_time()
        
        user_info = UserInfo(
            username=username,
            login_time=datetime.utcnow()
        )
        
        logger.info(f"用户登录成功: {username}")
        
        return LoginResponse(
            access_token=access_token,
            token_type="bearer",
            expires_in=expires_in,
            user_info=user_info
        )
        
    except Exception as e:
        logger.error(f"令牌生成失败: {username}, 错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="登录处理失败，请稍后重试"
        )


@router.get("/me", response_model=UserProfile, summary="获取当前用户信息")
async def get_current_user_info(current_user: str = Depends(get_current_user)):
    """
    获取当前登录用户的信息
    
    需要在请求头中携带有效的JWT令牌：
    Authorization: Bearer <token>
    """
    logger.debug(f"获取用户信息: {current_user}")
    
    return UserProfile(
        username=current_user,
        is_authenticated=True,
        login_time=datetime.utcnow()
    )


@router.post("/logout", response_model=AuthResponse, summary="用户登出")
async def logout(current_user: str = Depends(get_current_user)):
    """
    用户登出接口
    
    注意：由于JWT是无状态的，服务端无法主动使令牌失效。
    客户端应该删除本地存储的令牌。
    """
    logger.info(f"用户登出: {current_user}")
    
    return AuthResponse(
        success=True,
        message="登出成功",
        data={"username": current_user}
    )


@router.get("/verify", response_model=AuthResponse, summary="验证令牌")
async def verify_token(current_user: str = Depends(get_current_user)):
    """
    验证JWT令牌是否有效
    
    用于客户端检查令牌状态
    """
    logger.debug(f"令牌验证: {current_user}")
    
    return AuthResponse(
        success=True,
        message="令牌有效",
        data={
            "username": current_user,
            "is_authenticated": True
        }
    )
