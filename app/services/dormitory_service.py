"""
宿舍业务服务
"""
from typing import List, Optional
from datetime import date
from sqlalchemy.orm import Session
from fastapi import HTTPException, status

from app.repositories.dormitory_repo import DormitoryRepository
from app.repositories.record_repo import RecordRepository
from app.schemas.dormitory import (
    DormitoryCreate, DormitoryUpdate, DormitoryResponse, 
    DormitoryBedStatus, BedInfo
)
from app.core.logging import get_logger

logger = get_logger(__name__)


class DormitoryService:
    """宿舍服务类"""
    
    def __init__(self, db: Session):
        self.db = db
        self.repo = DormitoryRepository(db)
        self.record_repo = RecordRepository(db)
    
    async def get_dormitories(
        self, 
        skip: int = 0, 
        limit: int = 100, 
        is_active: Optional[bool] = None
    ) -> List[DormitoryResponse]:
        """获取宿舍列表"""
        try:
            filters = {}
            if is_active is not None:
                filters["is_active"] = is_active
            
            dormitories = self.repo.get_by_filters(filters, skip, limit)
            
            # 添加床位占用信息
            result = []
            for dorm in dormitories:
                occupied_beds = self.repo.get_occupied_beds_count(dorm.id)
                available_beds = dorm.total_beds - occupied_beds

                # 手动构建字典，然后创建Pydantic模型
                dorm_dict = {
                    "id": dorm.id,
                    "name": dorm.name,
                    "total_beds": dorm.total_beds,
                    "description": dorm.description,
                    "department_id": dorm.department_id,
                    "department_name": dorm.department.name if dorm.department else None,
                    "is_active": dorm.is_active,
                    "created_at": dorm.created_at,
                    "updated_at": dorm.updated_at,
                    "occupied_beds": occupied_beds,
                    "available_beds": available_beds
                }
                result.append(DormitoryResponse(**dorm_dict))
            
            logger.info(f"获取宿舍列表成功，共{len(result)}条记录")
            return result
            
        except Exception as e:
            logger.error(f"获取宿舍列表失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="获取宿舍列表失败"
            )
    
    async def get_dormitory_by_id(self, dormitory_id: str) -> Optional[DormitoryResponse]:
        """根据ID获取宿舍详情"""
        try:
            dormitory = self.repo.get_by_id(dormitory_id)
            if not dormitory:
                return None
            
            occupied_beds = self.repo.get_occupied_beds_count(dormitory_id)
            available_beds = dormitory.total_beds - occupied_beds

            # 手动构建字典，然后创建Pydantic模型
            dorm_dict = {
                "id": dormitory.id,
                "name": dormitory.name,
                "total_beds": dormitory.total_beds,
                "description": dormitory.description,
                "department_id": dormitory.department_id,
                "department_name": dormitory.department.name if dormitory.department else None,
                "is_active": dormitory.is_active,
                "created_at": dormitory.created_at,
                "updated_at": dormitory.updated_at,
                "occupied_beds": occupied_beds,
                "available_beds": available_beds
            }
            dorm_data = DormitoryResponse(**dorm_dict)
            
            logger.info(f"获取宿舍详情成功: {dormitory.name}")
            return dorm_data
            
        except Exception as e:
            logger.error(f"获取宿舍详情失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="获取宿舍详情失败"
            )
    
    async def create_dormitory(self, dormitory_data: DormitoryCreate) -> DormitoryResponse:
        """创建宿舍"""
        try:
            # 检查名称是否已存在
            if self.repo.check_name_exists(dormitory_data.name):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"宿舍名称 '{dormitory_data.name}' 已存在"
                )
            
            # 创建宿舍
            dormitory = self.repo.create(dormitory_data.model_dump())

            # 手动构建字典，然后创建Pydantic模型
            dorm_dict = {
                "id": dormitory.id,
                "name": dormitory.name,
                "total_beds": dormitory.total_beds,
                "description": dormitory.description,
                "department_id": dormitory.department_id,
                "department_name": dormitory.department.name if dormitory.department else None,
                "is_active": dormitory.is_active,
                "created_at": dormitory.created_at,
                "updated_at": dormitory.updated_at,
                "occupied_beds": 0,
                "available_beds": dormitory.total_beds
            }
            result = DormitoryResponse(**dorm_dict)
            
            logger.info(f"创建宿舍成功: {dormitory.name}")
            return result
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"创建宿舍失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="创建宿舍失败"
            )
    
    async def update_dormitory(
        self, 
        dormitory_id: str, 
        dormitory_data: DormitoryUpdate
    ) -> Optional[DormitoryResponse]:
        """更新宿舍"""
        try:
            # 检查宿舍是否存在
            existing_dorm = self.repo.get_by_id(dormitory_id)
            if not existing_dorm:
                return None
            
            # 检查名称冲突
            if dormitory_data.name and self.repo.check_name_exists(
                dormitory_data.name, exclude_id=dormitory_id
            ):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"宿舍名称 '{dormitory_data.name}' 已存在"
                )
            
            # 如果要减少床位数，检查是否有冲突
            if dormitory_data.total_beds and dormitory_data.total_beds < existing_dorm.total_beds:
                occupied_beds = self.repo.get_occupied_beds_count(dormitory_id)
                if dormitory_data.total_beds < occupied_beds:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"无法将床位数减少到{dormitory_data.total_beds}，当前已占用{occupied_beds}个床位"
                    )
            
            # 更新宿舍
            update_data = dormitory_data.model_dump(exclude_unset=True)
            dormitory = self.repo.update(dormitory_id, update_data)
            
            if dormitory:
                occupied_beds = self.repo.get_occupied_beds_count(dormitory_id)
                available_beds = dormitory.total_beds - occupied_beds

                # 手动构建字典，然后创建Pydantic模型
                dorm_dict = {
                    "id": dormitory.id,
                    "name": dormitory.name,
                    "total_beds": dormitory.total_beds,
                    "description": dormitory.description,
                    "department_id": dormitory.department_id,
                    "department_name": dormitory.department.name if dormitory.department else None,
                    "is_active": dormitory.is_active,
                    "created_at": dormitory.created_at,
                    "updated_at": dormitory.updated_at,
                    "occupied_beds": occupied_beds,
                    "available_beds": available_beds
                }
                result = DormitoryResponse(**dorm_dict)

                logger.info(f"更新宿舍成功: {dormitory.name}")
                return result
            
            return None
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"更新宿舍失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="更新宿舍失败"
            )
    
    async def delete_dormitory(self, dormitory_id: str) -> bool:
        """删除宿舍"""
        try:
            # 检查是否有入住记录
            active_records = self.record_repo.get_records_by_dormitory(dormitory_id, status='ACTIVE')
            if active_records:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"该宿舍还有 {len(active_records)} 条活跃入住记录，无法删除"
                )
            
            success = self.repo.delete(dormitory_id)
            if success:
                logger.info(f"删除宿舍成功: {dormitory_id}")
            
            return success
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"删除宿舍失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="删除宿舍失败"
            )
    
    async def get_bed_status(
        self, 
        dormitory_id: str, 
        target_date: str = None
    ) -> DormitoryBedStatus:
        """获取宿舍床位状态"""
        try:
            dormitory = self.repo.get_by_id(dormitory_id)
            if not dormitory:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="宿舍不存在"
                )
            
            # 解析日期
            if target_date:
                from datetime import datetime
                check_date = datetime.strptime(target_date, "%Y-%m-%d").date()
            else:
                check_date = date.today()
            
            # 获取指定日期的入住记录
            records = self.record_repo.get_records_by_date(check_date)
            dormitory_records = [r for r in records if r.dormitory_id == dormitory_id]
            
            # 构建床位详情
            bed_details = []
            occupied_bed_numbers = {r.bed_number: r for r in dormitory_records}
            
            for bed_num in range(1, dormitory.total_beds + 1):
                if bed_num in occupied_bed_numbers:
                    record = occupied_bed_numbers[bed_num]
                    bed_info = BedInfo(
                        bed_number=bed_num,
                        is_occupied=True,
                        resident_name=record.resident.name,
                        resident_id=record.resident.id,
                        check_in_date=record.check_in_date.isoformat()
                    )
                else:
                    bed_info = BedInfo(
                        bed_number=bed_num,
                        is_occupied=False
                    )
                bed_details.append(bed_info)
            
            occupied_count = len(occupied_bed_numbers)
            available_count = dormitory.total_beds - occupied_count
            
            return DormitoryBedStatus(
                dormitory_id=dormitory.id,
                dormitory_name=dormitory.name,
                total_beds=dormitory.total_beds,
                occupied_beds=occupied_count,
                available_beds=available_count,
                bed_details=bed_details
            )
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"获取床位状态失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="获取床位状态失败"
            )
