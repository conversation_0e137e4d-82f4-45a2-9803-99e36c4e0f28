"""
分摊计算核心服务
"""
from typing import List, Dict, Optional
from datetime import date, timedelta
from collections import defaultdict
from dataclasses import dataclass

from app.models.record import ResidenceRecord
from app.models.dormitory import Dormitory
from app.schemas.report import DepartmentSummary, DailyAllocationDetail


@dataclass
class DepartmentAllocation:
    """部门分摊信息"""
    department_id: str
    department_name: str
    bed_count: int
    allocation_ratio: float


@dataclass
class ResidentInfo:
    """住户信息"""
    id: str
    name: str
    department_name: str
    bed_number: int


@dataclass
class DailyAllocationResult:
    """日度分摊结果"""
    date: date
    dormitory_id: str
    dormitory_name: str
    total_beds: int
    occupied_beds: int
    residents: List[ResidentInfo]
    department_allocations: List[DepartmentAllocation]


class AllocationCalculator:
    """费用分摊计算器"""
    
    def __init__(self, unit_cost_per_bed_day: float = 100.0):
        """
        初始化计算器
        
        Args:
            unit_cost_per_bed_day: 每床每日费用单价
        """
        self.unit_cost = unit_cost_per_bed_day
    
    def calculate_daily_allocation(
        self, 
        target_date: date, 
        dormitory: Dormitory,
        residence_records: List[ResidenceRecord]
    ) -> DailyAllocationResult:
        """
        计算指定日期指定宿舍的分摊结果
        
        Args:
            target_date: 目标计算日期
            dormitory: 宿舍信息
            residence_records: 入住记录列表
            
        Returns:
            DailyAllocationResult: 日度分摊结果
        """
        # 1. 筛选有效的入住记录
        active_records = self._filter_active_records(target_date, dormitory.id, residence_records)

        # 2. 提取住户信息
        residents = self._extract_resident_info(active_records)

        # 3. 按部门分组统计
        department_stats = self._group_by_department(active_records)

        # 4. 计算分摊比例
        allocations = self._calculate_allocations(department_stats, dormitory.total_beds)

        return DailyAllocationResult(
            date=target_date,
            dormitory_id=dormitory.id,
            dormitory_name=dormitory.name,
            total_beds=dormitory.total_beds,
            occupied_beds=len(active_records),
            residents=residents,
            department_allocations=allocations
        )
    
    def calculate_monthly_summary(
        self, 
        year: int, 
        month: int,
        daily_allocations: List[DailyAllocationResult],
        end_date: Optional[date] = None
    ) -> List[DepartmentSummary]:
        """
        计算月度部门费用汇总
        
        Args:
            year: 年份
            month: 月份
            daily_allocations: 日度分摊结果列表
            end_date: 截止日期
            
        Returns:
            List[DepartmentSummary]: 部门费用汇总列表
        """
        # 汇总各部门床位天数
        department_bed_days = defaultdict(float)
        department_names = {}
        
        for daily_allocation in daily_allocations:
            for dept_alloc in daily_allocation.department_allocations:
                # 计算该部门在该日该宿舍的床位天数
                bed_days = daily_allocation.total_beds * dept_alloc.allocation_ratio
                department_bed_days[dept_alloc.department_id] += bed_days
                department_names[dept_alloc.department_id] = dept_alloc.department_name
        
        # 计算总床位天数
        total_bed_days = sum(department_bed_days.values())
        
        # 生成部门汇总列表
        summaries = []
        for dept_id, bed_days in department_bed_days.items():
            cost = bed_days * self.unit_cost
            ratio = bed_days / total_bed_days if total_bed_days > 0 else 0
            
            summaries.append(DepartmentSummary(
                department_id=dept_id,
                department_name=department_names[dept_id],
                bed_days=round(bed_days, 2),
                cost=round(cost, 2),
                ratio=round(ratio, 4)
            ))
        
        # 按床位天数降序排序
        summaries.sort(key=lambda x: x.bed_days, reverse=True)
        return summaries

    def _filter_active_records(
        self,
        target_date: date,
        dormitory_id: str,
        records: List[ResidenceRecord]
    ) -> List[ResidenceRecord]:
        """筛选指定日期在指定宿舍的有效入住记录"""
        active_records = []
        for record in records:
            if (record.dormitory_id == dormitory_id and
                record.status == 'ACTIVE' and
                record.check_in_date <= target_date and
                (record.check_out_date is None or record.check_out_date > target_date)):
                active_records.append(record)
        return active_records

    def _extract_resident_info(self, records: List[ResidenceRecord]) -> List[ResidentInfo]:
        """提取住户信息"""
        residents = []
        for record in records:
            if record.resident:
                residents.append(ResidentInfo(
                    id=record.resident.id,
                    name=record.resident.name,
                    department_name=record.resident.department.name if record.resident.department else "未分配部门",
                    bed_number=record.bed_number
                ))
        return residents

    def _group_by_department(self, records: List[ResidenceRecord]) -> Dict[str, Dict[str, int]]:
        """按部门分组统计住户数量"""
        department_stats = defaultdict(lambda: {"count": 0, "name": ""})

        for record in records:
            dept_id = record.resident.department_id
            dept_name = record.resident.department.name
            department_stats[dept_id]["count"] += 1
            department_stats[dept_id]["name"] = dept_name

        return dict(department_stats)

    def _calculate_allocations(
        self,
        department_stats: Dict[str, Dict[str, int]],
        total_beds: int
    ) -> List[DepartmentAllocation]:
        """计算各部门分摊比例"""
        allocations = []

        if not department_stats:
            # 空宿舍场景 - 公司承担100%
            allocations.append(DepartmentAllocation(
                department_id="company",
                department_name="公司",
                bed_count=total_beds,
                allocation_ratio=1.0
            ))
        else:
            # 有人入住场景 - 按部门数量平均分摊
            department_count = len(department_stats)
            ratio_per_department = 1.0 / department_count

            for dept_id, stats in department_stats.items():
                allocations.append(DepartmentAllocation(
                    department_id=dept_id,
                    department_name=stats["name"],
                    bed_count=stats["count"],
                    allocation_ratio=ratio_per_department
                ))

        return allocations

    def get_date_range(self, start_date: date, end_date: date) -> List[date]:
        """生成日期范围"""
        dates = []
        current_date = start_date
        while current_date <= end_date:
            dates.append(current_date)
            current_date += timedelta(days=1)
        return dates

    def get_month_date_range(
        self,
        year: int,
        month: int,
        end_date: Optional[date] = None
    ) -> List[date]:
        """获取月份日期范围"""
        # 月份开始日期
        month_start = date(year, month, 1)

        # 月份自然结束日期
        if month == 12:
            next_month = date(year + 1, 1, 1)
        else:
            next_month = date(year, month + 1, 1)
        month_end = next_month - timedelta(days=1)

        # 确定实际结束日期
        actual_end = min(end_date, month_end) if end_date else month_end

        return self.get_date_range(month_start, actual_end)
