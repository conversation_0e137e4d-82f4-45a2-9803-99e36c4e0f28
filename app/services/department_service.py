"""
部门业务服务
"""
from typing import List, Optional
from sqlalchemy.orm import Session
from fastapi import HTTPException, status

from app.repositories.department_repo import DepartmentRepository
from app.schemas.department import DepartmentCreate, DepartmentUpdate, DepartmentResponse
from app.core.logging import get_logger

logger = get_logger(__name__)


class DepartmentService:
    """部门服务类"""
    
    def __init__(self, db: Session):
        self.db = db
        self.repo = DepartmentRepository(db)
    
    async def get_departments(
        self, 
        skip: int = 0, 
        limit: int = 100, 
        is_active: Optional[bool] = None
    ) -> List[DepartmentResponse]:
        """获取部门列表"""
        try:
            filters = {}
            if is_active is not None:
                filters["is_active"] = is_active
            
            departments = self.repo.get_by_filters(filters, skip, limit)
            
            # 添加住户数量信息
            result = []
            for dept in departments:
                resident_count = self.repo.get_resident_count(dept.id)
                # 手动构建字典，然后创建Pydantic模型
                dept_dict = {
                    "id": dept.id,
                    "name": dept.name,
                    "description": dept.description,
                    "is_active": dept.is_active,
                    "created_at": dept.created_at,
                    "updated_at": dept.updated_at,
                    "resident_count": resident_count
                }
                result.append(DepartmentResponse(**dept_dict))
            
            logger.info(f"获取部门列表成功，共{len(result)}条记录")
            return result
            
        except Exception as e:
            logger.error(f"获取部门列表失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="获取部门列表失败"
            )
    
    async def get_department_by_id(self, department_id: str) -> Optional[DepartmentResponse]:
        """根据ID获取部门详情"""
        try:
            department = self.repo.get_by_id(department_id)
            if not department:
                return None
            
            resident_count = self.repo.get_resident_count(department_id)
            dept_data = DepartmentResponse.model_validate(department)
            dept_data.resident_count = resident_count
            
            logger.info(f"获取部门详情成功: {department.name}")
            return dept_data
            
        except Exception as e:
            logger.error(f"获取部门详情失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="获取部门详情失败"
            )
    
    async def create_department(self, department_data: DepartmentCreate) -> DepartmentResponse:
        """创建部门"""
        try:
            # 检查名称是否已存在
            if self.repo.check_name_exists(department_data.name):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"部门名称 '{department_data.name}' 已存在"
                )
            
            # 创建部门
            department = self.repo.create(department_data.model_dump())

            # 手动构建字典，然后创建Pydantic模型
            dept_dict = {
                "id": department.id,
                "name": department.name,
                "description": department.description,
                "is_active": department.is_active,
                "created_at": department.created_at,
                "updated_at": department.updated_at,
                "resident_count": 0
            }
            result = DepartmentResponse(**dept_dict)
            
            logger.info(f"创建部门成功: {department.name}")
            return result
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"创建部门失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="创建部门失败"
            )
    
    async def update_department(
        self, 
        department_id: str, 
        department_data: DepartmentUpdate
    ) -> Optional[DepartmentResponse]:
        """更新部门"""
        try:
            # 检查部门是否存在
            existing_dept = self.repo.get_by_id(department_id)
            if not existing_dept:
                return None
            
            # 检查名称冲突
            if department_data.name and self.repo.check_name_exists(
                department_data.name, exclude_id=department_id
            ):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"部门名称 '{department_data.name}' 已存在"
                )
            
            # 更新部门
            update_data = department_data.model_dump(exclude_unset=True)
            department = self.repo.update(department_id, update_data)
            
            if department:
                resident_count = self.repo.get_resident_count(department_id)
                result = DepartmentResponse.model_validate(department)
                result.resident_count = resident_count
                
                logger.info(f"更新部门成功: {department.name}")
                return result
            
            return None
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"更新部门失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="更新部门失败"
            )
    
    async def delete_department(self, department_id: str) -> bool:
        """删除部门"""
        try:
            # 检查是否有关联的住户
            resident_count = self.repo.get_resident_count(department_id)
            if resident_count > 0:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"该部门下还有 {resident_count} 名住户，无法删除"
                )
            
            success = self.repo.delete(department_id)
            if success:
                logger.info(f"删除部门成功: {department_id}")
            
            return success
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"删除部门失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="删除部门失败"
            )
