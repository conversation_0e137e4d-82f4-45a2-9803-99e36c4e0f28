"""
数据库配置模块
"""
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from typing import Generator

from .config import settings

# 创建数据库引擎
engine = create_engine(
    settings.database_url,
    echo=settings.database_echo,
    # SQLite特定配置
    connect_args={"check_same_thread": False} if "sqlite" in settings.database_url else {}
)

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def get_db() -> Generator[Session, None, None]:
    """
    获取数据库会话
    
    Yields:
        Session: 数据库会话
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def create_tables():
    """创建所有数据表"""
    # 确保所有模型都被导入
    from app.models import Department, Dormitory, Resident, ResidenceRecord, MonthlyReport, DailyAllocation
    from app.models.base import Base
    Base.metadata.create_all(bind=engine)


def drop_tables():
    """删除所有数据表"""
    from app.models.base import Base
    Base.metadata.drop_all(bind=engine)
