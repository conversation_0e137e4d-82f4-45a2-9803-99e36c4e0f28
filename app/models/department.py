"""
部门模型
"""
from sqlalchemy import Column, String, Text, Boolean
from sqlalchemy.orm import relationship
from .base import BaseModel


class Department(BaseModel):
    """部门模型"""
    __tablename__ = "departments"
    
    name = Column(String(100), nullable=False, unique=True, comment="部门名称")
    description = Column(Text, comment="部门描述")
    is_active = Column(Boolean, default=True, comment="是否启用")
    
    # 关联关系
    residents = relationship("Resident", back_populates="department", cascade="all, delete-orphan")
    dormitories = relationship("Dormitory", back_populates="department")
    
    def __repr__(self):
        return f"<Department(id={self.id}, name={self.name})>"
