"""
住户模型
"""
from sqlalchemy import Column, String, ForeignKey, Boolean
from sqlalchemy.orm import relationship
from .base import BaseModel


class Resident(BaseModel):
    """住户模型"""
    __tablename__ = "residents"
    
    name = Column(String(100), nullable=False, comment="姓名")
    employee_id = Column(String(50), unique=True, comment="员工号")
    phone = Column(String(20), comment="电话号码")
    email = Column(String(100), comment="邮箱地址")
    department_id = Column(String(50), ForeignKey("departments.id"), nullable=False, comment="部门ID")
    is_active = Column(Boolean, default=True, comment="是否启用")
    
    # 关联关系
    department = relationship("Department", back_populates="residents")
    residence_records = relationship("ResidenceRecord", back_populates="resident")
    
    def __repr__(self):
        return f"<Resident(id={self.id}, name={self.name}, employee_id={self.employee_id})>"
