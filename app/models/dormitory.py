"""
宿舍模型
"""
from sqlalchemy import Column, String, Integer, Text, Boolean, CheckConstraint, ForeignKey
from sqlalchemy.orm import relationship
from .base import BaseModel


class Dormitory(BaseModel):
    """宿舍模型"""
    __tablename__ = "dormitories"

    name = Column(String(100), nullable=False, unique=True, comment="宿舍名称")
    total_beds = Column(Integer, nullable=False, comment="床位总数")
    description = Column(Text, comment="宿舍描述")
    department_id = Column(String(50), ForeignKey("departments.id"), nullable=True, comment="所属部门ID")
    is_active = Column(Boolean, default=True, comment="是否启用")

    # 约束
    __table_args__ = (
        CheckConstraint('total_beds > 0', name='chk_total_beds_positive'),
    )

    # 关联关系 - 使用字符串引用避免循环导入
    department = relationship("Department", back_populates="dormitories")
    residence_records = relationship("ResidenceRecord", back_populates="dormitory", lazy="select")
    daily_allocations = relationship("DailyAllocation", back_populates="dormitory", lazy="select")

    def __repr__(self):
        return f"<Dormitory(id={self.id}, name={self.name}, beds={self.total_beds})>"
