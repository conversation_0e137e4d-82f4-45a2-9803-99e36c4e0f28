"""
部门数据访问层
"""
from typing import List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import func
from app.models.department import Department
from app.models.resident import Resident
from .base import BaseRepository


class DepartmentRepository(BaseRepository[Department]):
    """部门仓储类"""
    
    def __init__(self, db: Session):
        super().__init__(db, Department)
    
    def get_by_name(self, name: str) -> Optional[Department]:
        """根据名称获取部门"""
        return self.db.query(Department).filter(Department.name == name).first()
    
    def get_active_departments(self) -> List[Department]:
        """获取所有启用的部门"""
        return self.db.query(Department).filter(Department.is_active == True).all()
    
    def get_departments_with_resident_count(self, skip: int = 0, limit: int = 100) -> List[Department]:
        """获取部门列表，包含住户数量"""
        return (
            self.db.query(Department)
            .outerjoin(Resident)
            .group_by(Department.id)
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def get_resident_count(self, department_id: str) -> int:
        """获取部门住户数量"""
        return (
            self.db.query(func.count(Resident.id))
            .filter(Resident.department_id == department_id)
            .filter(Resident.is_active == True)
            .scalar() or 0
        )
    
    def check_name_exists(self, name: str, exclude_id: str = None) -> bool:
        """检查部门名称是否已存在"""
        query = self.db.query(Department).filter(Department.name == name)
        if exclude_id:
            query = query.filter(Department.id != exclude_id)
        return query.first() is not None
