"""
住户数据访问层
"""
from typing import List, Optional
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_
from app.models.resident import Resident
from app.models.department import Department
from app.models.record import ResidenceRecord
from .base import BaseRepository


class ResidentRepository(BaseRepository[Resident]):
    """住户仓储类"""
    
    def __init__(self, db: Session):
        super().__init__(db, Resident)
    
    def get_by_employee_id(self, employee_id: str) -> Optional[Resident]:
        """根据员工号获取住户"""
        return self.db.query(Resident).filter(Resident.employee_id == employee_id).first()
    
    def get_active_residents(self) -> List[Resident]:
        """获取所有启用的住户"""
        return (
            self.db.query(Resident)
            .filter(Resident.is_active == True)
            .options(joinedload(Resident.department))
            .all()
        )
    
    def get_residents_by_department(self, department_id: str) -> List[Resident]:
        """根据部门ID获取住户列表"""
        return (
            self.db.query(Resident)
            .filter(Resident.department_id == department_id)
            .filter(Resident.is_active == True)
            .options(joinedload(Resident.department))
            .all()
        )
    
    def get_residents_with_details(self, skip: int = 0, limit: int = 100) -> List[Resident]:
        """获取住户列表，包含部门和当前住宿信息"""
        return (
            self.db.query(Resident)
            .options(
                joinedload(Resident.department),
                joinedload(Resident.residence_records)
            )
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def get_current_residence(self, resident_id: str) -> Optional[ResidenceRecord]:
        """获取住户当前的入住记录"""
        return (
            self.db.query(ResidenceRecord)
            .filter(ResidenceRecord.resident_id == resident_id)
            .filter(ResidenceRecord.status == 'ACTIVE')
            .filter(ResidenceRecord.check_out_date.is_(None))
            .first()
        )
    
    def check_employee_id_exists(self, employee_id: str, exclude_id: str = None) -> bool:
        """检查员工号是否已存在"""
        if not employee_id:
            return False
        
        query = self.db.query(Resident).filter(Resident.employee_id == employee_id)
        if exclude_id:
            query = query.filter(Resident.id != exclude_id)
        return query.first() is not None
    
    def search_residents(self, keyword: str, department_id: str = None) -> List[Resident]:
        """搜索住户"""
        query = self.db.query(Resident).options(joinedload(Resident.department))
        
        if keyword:
            query = query.filter(
                or_(
                    Resident.name.contains(keyword),
                    Resident.employee_id.contains(keyword),
                    Resident.phone.contains(keyword),
                    Resident.email.contains(keyword)
                )
            )
        
        if department_id:
            query = query.filter(Resident.department_id == department_id)
        
        return query.all()
