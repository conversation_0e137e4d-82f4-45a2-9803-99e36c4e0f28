"""
基础仓储类
"""
from typing import Generic, TypeVar, Type, List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from app.models.base import BaseModel

ModelType = TypeVar("ModelType", bound=BaseModel)


class BaseRepository(Generic[ModelType]):
    """基础仓储类"""
    
    def __init__(self, db: Session, model: Type[ModelType]):
        self.db = db
        self.model = model
    
    def get_by_id(self, id: str) -> Optional[ModelType]:
        """根据ID获取单个记录"""
        return self.db.query(self.model).filter(self.model.id == id).first()
    
    def get_all(self, skip: int = 0, limit: int = 100) -> List[ModelType]:
        """获取所有记录"""
        return self.db.query(self.model).offset(skip).limit(limit).all()
    
    def get_by_filters(self, filters: Dict[str, Any], skip: int = 0, limit: int = 100) -> List[ModelType]:
        """根据筛选条件获取记录"""
        query = self.db.query(self.model)
        
        for field, value in filters.items():
            if value is not None:
                if hasattr(self.model, field):
                    query = query.filter(getattr(self.model, field) == value)
        
        return query.offset(skip).limit(limit).all()
    
    def create(self, obj_data: Dict[str, Any]) -> ModelType:
        """创建新记录"""
        db_obj = self.model(**obj_data)
        self.db.add(db_obj)
        self.db.commit()
        self.db.refresh(db_obj)
        return db_obj
    
    def update(self, id: str, obj_data: Dict[str, Any]) -> Optional[ModelType]:
        """更新记录"""
        db_obj = self.get_by_id(id)
        if not db_obj:
            return None
        
        for field, value in obj_data.items():
            if value is not None and hasattr(db_obj, field):
                setattr(db_obj, field, value)
        
        self.db.commit()
        self.db.refresh(db_obj)
        return db_obj
    
    def delete(self, id: str) -> bool:
        """删除记录"""
        db_obj = self.get_by_id(id)
        if not db_obj:
            return False
        
        self.db.delete(db_obj)
        self.db.commit()
        return True
    
    def count(self, filters: Dict[str, Any] = None) -> int:
        """统计记录数量"""
        query = self.db.query(self.model)
        
        if filters:
            for field, value in filters.items():
                if value is not None and hasattr(self.model, field):
                    query = query.filter(getattr(self.model, field) == value)
        
        return query.count()
    
    def exists(self, id: str) -> bool:
        """检查记录是否存在"""
        return self.db.query(self.model).filter(self.model.id == id).first() is not None
