"""
报表数据访问层
"""
from typing import List, Optional
from datetime import date
from sqlalchemy.orm import Session
from sqlalchemy import and_
from app.models.report import MonthlyReport, DailyAllocation
from .base import BaseRepository


class ReportRepository(BaseRepository[MonthlyReport]):
    """报表仓储类"""
    
    def __init__(self, db: Session):
        super().__init__(db, MonthlyReport)
    
    def get_by_year_month(self, year: int, month: int) -> Optional[MonthlyReport]:
        """根据年月获取月度报告"""
        return (
            self.db.query(MonthlyReport)
            .filter(MonthlyReport.year == year)
            .filter(MonthlyReport.month == month)
            .first()
        )
    
    def create_monthly_report(
        self,
        year: int,
        month: int,
        total_bed_days: float,
        department_summary: List[dict],
        daily_details: List[dict]
    ) -> MonthlyReport:
        """创建月度报告"""
        # 删除已存在的报告
        existing = self.get_by_year_month(year, month)
        if existing:
            self.db.delete(existing)
            self.db.flush()
        
        report = MonthlyReport(
            year=year,
            month=month,
            report_date=date.today(),
            total_bed_days=total_bed_days,
            department_summary=department_summary,
            daily_details=daily_details
        )
        
        self.db.add(report)
        self.db.commit()
        self.db.refresh(report)
        return report
    
    def get_reports_by_year(self, year: int) -> List[MonthlyReport]:
        """获取指定年份的所有月度报告"""
        return (
            self.db.query(MonthlyReport)
            .filter(MonthlyReport.year == year)
            .order_by(MonthlyReport.month)
            .all()
        )
    
    def get_recent_reports(self, limit: int = 12) -> List[MonthlyReport]:
        """获取最近的月度报告"""
        return (
            self.db.query(MonthlyReport)
            .order_by(MonthlyReport.year.desc(), MonthlyReport.month.desc())
            .limit(limit)
            .all()
        )


class DailyAllocationRepository(BaseRepository[DailyAllocation]):
    """日度分摊仓储类"""
    
    def __init__(self, db: Session):
        super().__init__(db, DailyAllocation)
    
    def get_by_date_range(
        self, 
        start_date: date, 
        end_date: date, 
        dormitory_id: str = None
    ) -> List[DailyAllocation]:
        """根据日期范围获取日度分摊记录"""
        query = (
            self.db.query(DailyAllocation)
            .filter(
                and_(
                    DailyAllocation.allocation_date >= start_date,
                    DailyAllocation.allocation_date <= end_date
                )
            )
        )
        
        if dormitory_id:
            query = query.filter(DailyAllocation.dormitory_id == dormitory_id)
        
        return query.order_by(DailyAllocation.allocation_date).all()
    
    def get_by_date(self, target_date: date) -> List[DailyAllocation]:
        """获取指定日期的分摊记录"""
        return (
            self.db.query(DailyAllocation)
            .filter(DailyAllocation.allocation_date == target_date)
            .all()
        )
    
    def create_daily_allocation(
        self,
        allocation_date: date,
        dormitory_id: str,
        total_beds: int,
        occupied_beds: int,
        department_allocations: List[dict],
        monthly_report_id: str = None
    ) -> DailyAllocation:
        """创建日度分摊记录"""
        allocation = DailyAllocation(
            allocation_date=allocation_date,
            dormitory_id=dormitory_id,
            total_beds=total_beds,
            occupied_beds=occupied_beds,
            department_allocations=department_allocations,
            monthly_report_id=monthly_report_id
        )
        
        self.db.add(allocation)
        self.db.commit()
        self.db.refresh(allocation)
        return allocation
    
    def delete_by_date_range(self, start_date: date, end_date: date):
        """删除指定日期范围的分摊记录"""
        self.db.query(DailyAllocation).filter(
            and_(
                DailyAllocation.allocation_date >= start_date,
                DailyAllocation.allocation_date <= end_date
            )
        ).delete()
        self.db.commit()
