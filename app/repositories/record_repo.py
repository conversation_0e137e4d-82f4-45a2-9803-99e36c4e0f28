"""
入住记录数据访问层
"""
from typing import List, Optional
from datetime import date
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func
from app.models.record import ResidenceRecord
from app.models.resident import Resident
from app.models.dormitory import Dormitory
from app.models.department import Department
from .base import BaseRepository


class RecordRepository(BaseRepository[ResidenceRecord]):
    """入住记录仓储类"""
    
    def __init__(self, db: Session):
        super().__init__(db, ResidenceRecord)
    
    def get_records_with_details(self, skip: int = 0, limit: int = 100) -> List[ResidenceRecord]:
        """获取入住记录列表，包含关联信息"""
        return (
            self.db.query(ResidenceRecord)
            .options(
                joinedload(ResidenceRecord.resident).joinedload(Resident.department),
                joinedload(ResidenceRecord.dormitory)
            )
            .order_by(ResidenceRecord.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def get_active_records(self) -> List[ResidenceRecord]:
        """获取所有活跃的入住记录"""
        return (
            self.db.query(ResidenceRecord)
            .filter(ResidenceRecord.status == 'ACTIVE')
            .options(
                joinedload(ResidenceRecord.resident).joinedload(Resident.department),
                joinedload(ResidenceRecord.dormitory)
            )
            .all()
        )

    def get_records_in_date_range(self, start_date: date, end_date: date) -> List[ResidenceRecord]:
        """获取指定日期范围内的入住记录"""
        return (
            self.db.query(ResidenceRecord)
            .filter(
                and_(
                    ResidenceRecord.check_in_date <= end_date,
                    or_(
                        ResidenceRecord.check_out_date.is_(None),
                        ResidenceRecord.check_out_date >= start_date
                    )
                )
            )
            .options(
                joinedload(ResidenceRecord.resident).joinedload(Resident.department),
                joinedload(ResidenceRecord.dormitory)
            )
            .all()
        )
    
    def get_records_by_dormitory(self, dormitory_id: str, status: str = None) -> List[ResidenceRecord]:
        """根据宿舍ID获取入住记录"""
        query = (
            self.db.query(ResidenceRecord)
            .filter(ResidenceRecord.dormitory_id == dormitory_id)
            .options(
                joinedload(ResidenceRecord.resident).joinedload(Resident.department)
            )
        )
        
        if status:
            query = query.filter(ResidenceRecord.status == status)
        
        return query.order_by(ResidenceRecord.check_in_date.desc()).all()
    
    def get_records_by_resident(self, resident_id: str) -> List[ResidenceRecord]:
        """根据住户ID获取入住记录"""
        return (
            self.db.query(ResidenceRecord)
            .filter(ResidenceRecord.resident_id == resident_id)
            .options(joinedload(ResidenceRecord.dormitory))
            .order_by(ResidenceRecord.check_in_date.desc())
            .all()
        )
    
    def get_records_in_date_range(self, start_date: date, end_date: date) -> List[ResidenceRecord]:
        """获取指定日期范围内的入住记录"""
        return (
            self.db.query(ResidenceRecord)
            .filter(
                and_(
                    ResidenceRecord.check_in_date <= end_date,
                    or_(
                        ResidenceRecord.check_out_date.is_(None),
                        ResidenceRecord.check_out_date >= start_date
                    )
                )
            )
            .options(
                joinedload(ResidenceRecord.resident).joinedload(Resident.department),
                joinedload(ResidenceRecord.dormitory)
            )
            .all()
        )
    
    def get_records_by_date(self, target_date: date) -> List[ResidenceRecord]:
        """获取指定日期的入住记录"""
        return (
            self.db.query(ResidenceRecord)
            .filter(ResidenceRecord.status == 'ACTIVE')
            .filter(ResidenceRecord.check_in_date <= target_date)
            .filter(
                or_(
                    ResidenceRecord.check_out_date.is_(None),
                    ResidenceRecord.check_out_date > target_date
                )
            )
            .options(
                joinedload(ResidenceRecord.resident).joinedload(Resident.department),
                joinedload(ResidenceRecord.dormitory)
            )
            .all()
        )
    
    def check_bed_conflict(
        self, 
        dormitory_id: str, 
        bed_number: int, 
        check_in_date: date,
        check_out_date: date = None,
        exclude_record_id: str = None
    ) -> bool:
        """检查床位是否有冲突"""
        query = (
            self.db.query(ResidenceRecord)
            .filter(ResidenceRecord.dormitory_id == dormitory_id)
            .filter(ResidenceRecord.bed_number == bed_number)
            .filter(ResidenceRecord.status == 'ACTIVE')
        )
        
        if exclude_record_id:
            query = query.filter(ResidenceRecord.id != exclude_record_id)
        
        # 检查时间段冲突
        if check_out_date:
            query = query.filter(
                and_(
                    ResidenceRecord.check_in_date < check_out_date,
                    or_(
                        ResidenceRecord.check_out_date.is_(None),
                        ResidenceRecord.check_out_date > check_in_date
                    )
                )
            )
        else:
            query = query.filter(
                or_(
                    ResidenceRecord.check_out_date.is_(None),
                    ResidenceRecord.check_out_date > check_in_date
                )
            )
        
        return query.first() is not None
    
    def get_resident_current_record(self, resident_id: str) -> Optional[ResidenceRecord]:
        """获取住户当前的入住记录"""
        return (
            self.db.query(ResidenceRecord)
            .filter(ResidenceRecord.resident_id == resident_id)
            .filter(ResidenceRecord.status == 'ACTIVE')
            .filter(ResidenceRecord.check_out_date.is_(None))
            .options(joinedload(ResidenceRecord.dormitory))
            .first()
        )
    
    def get_statistics_by_department(self, start_date: date, end_date: date) -> List[dict]:
        """按部门统计入住情况"""
        return (
            self.db.query(
                Department.name.label('department_name'),
                func.count(ResidenceRecord.id).label('record_count'),
                func.count(func.distinct(ResidenceRecord.resident_id)).label('resident_count')
            )
            .join(Resident, ResidenceRecord.resident_id == Resident.id)
            .join(Department, Resident.department_id == Department.id)
            .filter(ResidenceRecord.check_in_date <= end_date)
            .filter(
                or_(
                    ResidenceRecord.check_out_date.is_(None),
                    ResidenceRecord.check_out_date >= start_date
                )
            )
            .group_by(Department.id, Department.name)
            .all()
        )
