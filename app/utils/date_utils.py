"""
日期工具类
"""
from datetime import date, datetime, timedelta
from typing import List, Tuple
import calendar


class DateUtils:
    """日期工具类"""
    
    @staticmethod
    def get_month_range(year: int, month: int) -> Tuple[date, date]:
        """
        获取指定年月的日期范围
        
        Args:
            year: 年份
            month: 月份
            
        Returns:
            Tuple[date, date]: (月初日期, 月末日期)
        """
        start_date = date(year, month, 1)
        _, last_day = calendar.monthrange(year, month)
        end_date = date(year, month, last_day)
        return start_date, end_date
    
    @staticmethod
    def get_date_list(start_date: date, end_date: date) -> List[date]:
        """
        获取日期范围内的所有日期列表
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            List[date]: 日期列表
        """
        dates = []
        current_date = start_date
        while current_date <= end_date:
            dates.append(current_date)
            current_date += timedelta(days=1)
        return dates
    
    @staticmethod
    def calculate_days_between(start_date: date, end_date: date) -> int:
        """
        计算两个日期之间的天数
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            int: 天数差
        """
        return (end_date - start_date).days
    
    @staticmethod
    def is_date_in_range(target_date: date, start_date: date, end_date: date = None) -> bool:
        """
        检查日期是否在指定范围内
        
        Args:
            target_date: 目标日期
            start_date: 开始日期
            end_date: 结束日期，None表示无结束日期
            
        Returns:
            bool: 是否在范围内
        """
        if target_date < start_date:
            return False
        if end_date is not None and target_date > end_date:
            return False
        return True
    
    @staticmethod
    def format_date(target_date: date, format_str: str = "%Y-%m-%d") -> str:
        """
        格式化日期
        
        Args:
            target_date: 目标日期
            format_str: 格式字符串
            
        Returns:
            str: 格式化后的日期字符串
        """
        return target_date.strftime(format_str)
    
    @staticmethod
    def parse_date(date_str: str, format_str: str = "%Y-%m-%d") -> date:
        """
        解析日期字符串
        
        Args:
            date_str: 日期字符串
            format_str: 格式字符串
            
        Returns:
            date: 解析后的日期对象
        """
        return datetime.strptime(date_str, format_str).date()
    
    @staticmethod
    def get_current_month_range() -> Tuple[date, date]:
        """
        获取当前月份的日期范围
        
        Returns:
            Tuple[date, date]: (月初日期, 月末日期)
        """
        today = date.today()
        return DateUtils.get_month_range(today.year, today.month)
    
    @staticmethod
    def get_quarter_range(year: int, quarter: int) -> Tuple[date, date]:
        """
        获取指定季度的日期范围
        
        Args:
            year: 年份
            quarter: 季度 (1-4)
            
        Returns:
            Tuple[date, date]: (季度开始日期, 季度结束日期)
        """
        if quarter not in [1, 2, 3, 4]:
            raise ValueError("季度必须是1-4之间的整数")
        
        start_month = (quarter - 1) * 3 + 1
        end_month = quarter * 3
        
        start_date = date(year, start_month, 1)
        _, last_day = calendar.monthrange(year, end_month)
        end_date = date(year, end_month, last_day)
        
        return start_date, end_date
