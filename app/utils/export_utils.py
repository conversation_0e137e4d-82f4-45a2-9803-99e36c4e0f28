"""
导出工具类
"""
import io
from typing import Tuple, Any, Dict, List
from datetime import date
import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows
from reportlab.lib import colors
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont


class ExportUtils:
    """导出工具类"""
    
    def __init__(self):
        self.styles = getSampleStyleSheet()
        # 尝试注册中文字体，如果失败则使用默认字体
        try:
            # 这里可以根据系统情况调整字体路径
            # pdfmetrics.registerFont(TTFont('SimHei', 'SimHei.ttf'))
            pass
        except:
            pass
    
    async def export_to_excel(self, report_data: Dict[str, Any]) -> Tuple[bytes, str, str]:
        """
        导出Excel格式报告
        
        Args:
            report_data: 报告数据
            
        Returns:
            Tuple[bytes, str, str]: (文件内容, 文件名, MIME类型)
        """
        # 创建工作簿
        wb = Workbook()
        ws = wb.active
        ws.title = "月度报告"
        
        # 设置标题
        title = f"{report_data['year']}年{report_data['month']}月宿舍费用分摊报告"
        ws['A1'] = title
        ws['A1'].font = Font(size=16, bold=True)
        ws['A1'].alignment = Alignment(horizontal='center')
        ws.merge_cells('A1:F1')
        
        # 基本信息
        row = 3
        ws[f'A{row}'] = "报告日期:"
        ws[f'B{row}'] = report_data.get('end_date', '')
        ws[f'D{row}'] = "平均入住率:"
        ws[f'E{row}'] = f"{report_data.get('average_occupancy_rate', 0)}%"
        
        row += 1
        ws[f'A{row}'] = "部门数量:"
        ws[f'B{row}'] = report_data.get('department_count', 0)
        ws[f'D{row}'] = "宿舍数量:"
        ws[f'E{row}'] = report_data.get('dormitory_count', 0)
        
        # 宿舍分摊明细表头
        row += 3
        headers = ['宿舍名称', '总床位天数', '分摊比例(%)', '主要部门', '部门床位天数', '部门占比(%)']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=row, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
            cell.alignment = Alignment(horizontal='center')
        
        # 填充数据
        row += 1
        for dorm_data in report_data.get('dormitory_allocations', []):
            dorm_name = dorm_data.get('dormitory_name', '')
            total_bed_days = dorm_data.get('total_bed_days', 0)
            allocation_percentage = dorm_data.get('allocation_percentage', 0)
            
            departments = dorm_data.get('departments', {})
            if departments:
                # 按床位天数排序，显示主要部门
                sorted_depts = sorted(departments.items(), 
                                    key=lambda x: x[1].get('bed_days', 0), reverse=True)
                
                for i, (dept_id, dept_data) in enumerate(sorted_depts):
                    current_row = row + i
                    if i == 0:  # 第一行显示宿舍信息
                        ws.cell(row=current_row, column=1, value=dorm_name)
                        ws.cell(row=current_row, column=2, value=total_bed_days)
                        ws.cell(row=current_row, column=3, value=allocation_percentage)
                    
                    ws.cell(row=current_row, column=4, value=dept_data.get('department_name', ''))
                    ws.cell(row=current_row, column=5, value=dept_data.get('bed_days', 0))
                    ws.cell(row=current_row, column=6, value=dept_data.get('percentage_in_dorm', 0))
                
                row += len(sorted_depts)
            else:
                ws.cell(row=row, column=1, value=dorm_name)
                ws.cell(row=row, column=2, value=total_bed_days)
                ws.cell(row=row, column=3, value=allocation_percentage)
                ws.cell(row=row, column=4, value='无')
                row += 1
        
        # 设置列宽
        column_widths = [20, 15, 15, 20, 15, 15]
        for i, width in enumerate(column_widths, 1):
            ws.column_dimensions[chr(64 + i)].width = width
        
        # 保存到内存
        output = io.BytesIO()
        wb.save(output)
        output.seek(0)
        
        filename = f"月度报告_{report_data['year']}年{report_data['month']}月.xlsx"
        return output.getvalue(), filename, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    
    async def export_to_csv(self, report_data: Dict[str, Any]) -> Tuple[bytes, str, str]:
        """
        导出CSV格式报告
        
        Args:
            report_data: 报告数据
            
        Returns:
            Tuple[bytes, str, str]: (文件内容, 文件名, MIME类型)
        """
        # 准备数据
        rows = []
        
        # 添加标题行
        rows.append([f"{report_data['year']}年{report_data['month']}月宿舍费用分摊报告"])
        rows.append([])  # 空行
        
        # 基本信息
        rows.append(['报告日期', report_data.get('end_date', '')])
        rows.append(['平均入住率', f"{report_data.get('average_occupancy_rate', 0)}%"])
        rows.append(['部门数量', report_data.get('department_count', 0)])
        rows.append(['宿舍数量', report_data.get('dormitory_count', 0)])
        rows.append([])  # 空行
        
        # 表头
        rows.append(['宿舍名称', '总床位天数', '分摊比例(%)', '部门名称', '部门床位天数', '部门占比(%)'])
        
        # 数据行
        for dorm_data in report_data.get('dormitory_allocations', []):
            dorm_name = dorm_data.get('dormitory_name', '')
            total_bed_days = dorm_data.get('total_bed_days', 0)
            allocation_percentage = dorm_data.get('allocation_percentage', 0)
            
            departments = dorm_data.get('departments', {})
            if departments:
                sorted_depts = sorted(departments.items(), 
                                    key=lambda x: x[1].get('bed_days', 0), reverse=True)
                
                for i, (dept_id, dept_data) in enumerate(sorted_depts):
                    if i == 0:  # 第一行显示宿舍信息
                        rows.append([
                            dorm_name,
                            total_bed_days,
                            allocation_percentage,
                            dept_data.get('department_name', ''),
                            dept_data.get('bed_days', 0),
                            dept_data.get('percentage_in_dorm', 0)
                        ])
                    else:
                        rows.append([
                            '',  # 宿舍名称空白
                            '',  # 总床位天数空白
                            '',  # 分摊比例空白
                            dept_data.get('department_name', ''),
                            dept_data.get('bed_days', 0),
                            dept_data.get('percentage_in_dorm', 0)
                        ])
            else:
                rows.append([dorm_name, total_bed_days, allocation_percentage, '无', 0, 0])
        
        # 转换为DataFrame并导出CSV
        df = pd.DataFrame(rows)
        output = io.StringIO()
        df.to_csv(output, index=False, header=False, encoding='utf-8-sig')
        
        filename = f"月度报告_{report_data['year']}年{report_data['month']}月.csv"
        return output.getvalue().encode('utf-8-sig'), filename, "text/csv"

    async def export_to_pdf(self, report_data: Dict[str, Any]) -> Tuple[bytes, str, str]:
        """
        导出PDF格式报告

        Args:
            report_data: 报告数据

        Returns:
            Tuple[bytes, str, str]: (文件内容, 文件名, MIME类型)
        """
        output = io.BytesIO()
        doc = SimpleDocTemplate(output, pagesize=A4)
        story = []

        # 标题
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=self.styles['Heading1'],
            fontSize=18,
            spaceAfter=30,
            alignment=1  # 居中
        )
        title = f"{report_data['year']}年{report_data['month']}月宿舍费用分摊报告"
        story.append(Paragraph(title, title_style))

        # 基本信息
        info_data = [
            ['报告日期', report_data.get('end_date', ''), '平均入住率', f"{report_data.get('average_occupancy_rate', 0)}%"],
            ['部门数量', str(report_data.get('department_count', 0)), '宿舍数量', str(report_data.get('dormitory_count', 0))]
        ]

        info_table = Table(info_data, colWidths=[2*inch, 1.5*inch, 2*inch, 1.5*inch])
        info_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, -1), colors.lightgrey),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
            ('BACKGROUND', (0, 0), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        story.append(info_table)
        story.append(Spacer(1, 20))

        # 宿舍分摊明细表
        table_data = [['宿舍名称', '总床位天数', '分摊比例(%)', '部门名称', '部门床位天数', '部门占比(%)']]

        for dorm_data in report_data.get('dormitory_allocations', []):
            dorm_name = dorm_data.get('dormitory_name', '')
            total_bed_days = dorm_data.get('total_bed_days', 0)
            allocation_percentage = dorm_data.get('allocation_percentage', 0)

            departments = dorm_data.get('departments', {})
            if departments:
                sorted_depts = sorted(departments.items(),
                                    key=lambda x: x[1].get('bed_days', 0), reverse=True)

                for i, (dept_id, dept_data) in enumerate(sorted_depts):
                    if i == 0:  # 第一行显示宿舍信息
                        table_data.append([
                            dorm_name,
                            str(total_bed_days),
                            f"{allocation_percentage}%",
                            dept_data.get('department_name', ''),
                            str(dept_data.get('bed_days', 0)),
                            f"{dept_data.get('percentage_in_dorm', 0)}%"
                        ])
                    else:
                        table_data.append([
                            '',  # 宿舍名称空白
                            '',  # 总床位天数空白
                            '',  # 分摊比例空白
                            dept_data.get('department_name', ''),
                            str(dept_data.get('bed_days', 0)),
                            f"{dept_data.get('percentage_in_dorm', 0)}%"
                        ])
            else:
                table_data.append([dorm_name, str(total_bed_days), f"{allocation_percentage}%", '无', '0', '0%'])

        # 创建表格
        detail_table = Table(table_data, colWidths=[1.2*inch, 1*inch, 1*inch, 1.2*inch, 1*inch, 1*inch])
        detail_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 9),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        story.append(detail_table)

        # 构建PDF
        doc.build(story)
        output.seek(0)

        filename = f"月度报告_{report_data['year']}年{report_data['month']}月.pdf"
        return output.getvalue(), filename, "application/pdf"
