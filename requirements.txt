# FastAPI核心依赖
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# 数据库相关
sqlalchemy==2.0.23
alembic==1.12.1
# psycopg2-binary==2.9.9  # PostgreSQL驱动（生产环境需要）

# 数据验证和序列化
pydantic==2.5.0
pydantic-settings==2.1.0

# 日期时间处理
python-dateutil==2.8.2

# 文件导出
openpyxl==3.1.2
reportlab==4.0.7
pandas==2.1.4

# HTTP客户端
httpx==0.25.2

# 日志
loguru==0.7.2

# 开发工具
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
black==23.11.0
isort==5.12.0
flake8==6.1.0

# 安全
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4

# LDAP认证
ldap3==2.9.1

# 环境变量
python-dotenv==1.0.0