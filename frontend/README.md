# 宿舍入住管理系统 - 前端

基于 Vue 3 + Element Plus + Vite 构建的现代化宿舍管理系统前端应用。

## 🚀 技术栈

- **框架**: Vue 3 (Composition API)
- **构建工具**: Vite 5
- **UI 组件库**: Element Plus
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **HTTP 客户端**: Axios
- **图表库**: ECharts + Vue-ECharts
- **样式**: SCSS
- **图标**: Element Plus Icons
- **日期处理**: Day.js

## 📦 功能特性

### 核心功能
- 🏢 **部门管理** - 部门的增删改查，支持启用/禁用状态
- 🏠 **宿舍管理** - 宿舍信息管理，床位状态可视化
- 👥 **住户管理** - 住户信息管理（开发中）
- 📋 **入住记录** - 入住/退房记录管理（开发中）
- 📊 **报表统计** - 实时报告、月度报告、年度汇总

### 技术特性
- 📱 **响应式设计** - 支持桌面端和移动端
- 🎨 **现代化UI** - 基于Element Plus的美观界面
- 🔄 **实时数据** - 支持数据实时刷新
- 📈 **数据可视化** - ECharts图表展示
- 🌙 **主题切换** - 支持明暗主题切换
- 🚀 **性能优化** - 代码分割、懒加载
- 🔒 **类型安全** - TypeScript支持（可选）

## 🛠️ 开发环境

### 环境要求
- Node.js >= 16.0.0
- npm >= 8.0.0 或 yarn >= 1.22.0

### 安装依赖
```bash
# 使用 npm
npm install

# 使用 yarn
yarn install

# 使用 pnpm
pnpm install
```

### 开发服务器
```bash
# 启动开发服务器
npm run dev

# 或使用 yarn
yarn dev

# 或使用 pnpm
pnpm dev
```

访问 http://localhost:3000

### 构建生产版本
```bash
# 构建生产版本
npm run build

# 预览生产版本
npm run preview
```

## 📁 项目结构

```
frontend/
├── public/                 # 静态资源
├── src/
│   ├── api/               # API 接口
│   │   ├── request.js     # Axios 配置
│   │   ├── departments.js # 部门 API
│   │   ├── dormitories.js # 宿舍 API
│   │   └── reports.js     # 报表 API
│   ├── components/        # 公共组件
│   │   └── Layout/        # 布局组件
│   ├── router/            # 路由配置
│   ├── stores/            # Pinia 状态管理
│   ├── styles/            # 全局样式
│   ├── views/             # 页面组件
│   ├── App.vue           # 根组件
│   └── main.js           # 入口文件
├── .env                   # 环境变量
├── .env.development      # 开发环境变量
├── .env.production       # 生产环境变量
├── index.html            # HTML 模板
├── package.json          # 项目配置
├── vite.config.js        # Vite 配置
└── README.md             # 项目说明
```

## 🔧 配置说明

### 环境变量
- `VITE_API_BASE_URL`: 后端 API 基础地址
- `VITE_APP_TITLE`: 应用标题
- `VITE_APP_VERSION`: 应用版本

### 代理配置
开发环境下，Vite 会自动将 `/api` 请求代理到后端服务器。

## 📋 页面说明

### 仪表板 (`/dashboard`)
- 显示系统概览统计
- 部门和宿舍数量统计
- 床位使用情况图表
- 快速操作入口

### 部门管理 (`/departments`)
- 部门列表展示
- 新增/编辑/删除部门
- 部门状态管理
- 搜索和筛选功能

### 宿舍管理 (`/dormitories`)
- 宿舍列表展示
- 床位状态可视化
- 入住率统计
- 宿舍信息管理

### 报表统计 (`/reports`)
- 实时数据报告
- 月度费用分摊报告
- 年度统计汇总
- 数据导出功能

## 🎨 UI 组件

项目使用 Element Plus 作为 UI 组件库，主要组件包括：
- 表格 (Table)
- 表单 (Form)
- 对话框 (Dialog)
- 按钮 (Button)
- 图表 (Charts)
- 统计卡片 (Statistics)

## 🔄 状态管理

使用 Pinia 进行状态管理，主要 Store：
- `useDepartmentStore`: 部门数据管理
- `useDormitoryStore`: 宿舍数据管理

## 🚀 部署

### 构建
```bash
npm run build
```

### 部署到 Nginx
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/dist;
    index index.html;
    
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    location /api {
        proxy_pass http://backend-server:8001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License
