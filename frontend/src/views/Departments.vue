<template>
  <div class="departments">
    <div class="app-container">
        <!-- 页面标题 -->
        <div class="page-header">
          <h1 class="page-title">部门管理</h1>
          <div class="page-actions">
            <el-button type="primary" @click="showCreateDialog">
              <el-icon><Plus /></el-icon>
              新增部门
            </el-button>
            <el-button @click="refreshData">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>

        <!-- 搜索筛选 -->
        <div class="app-card">
          <el-form :model="searchForm" inline>
            <el-form-item label="部门名称">
              <el-input
                v-model="searchForm.name"
                placeholder="请输入部门名称"
                clearable
                style="width: 200px"
              />
            </el-form-item>
            <el-form-item label="状态">
              <el-select
                v-model="searchForm.is_active"
                placeholder="请选择状态"
                clearable
                style="width: 120px"
              >
                <el-option label="启用" :value="true" />
                <el-option label="禁用" :value="false" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">
                <el-icon><Search /></el-icon>
                搜索
              </el-button>
              <el-button @click="resetSearch">
                <el-icon><RefreshLeft /></el-icon>
                重置
              </el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 数据表格 -->
        <div class="app-card">
          <el-table
            v-loading="departmentStore.loading"
            :data="filteredDepartments"
            stripe
            style="width: 100%"
          >
            <el-table-column prop="name" label="部门名称" min-width="150" />
            <el-table-column prop="description" label="描述" min-width="200">
              <template #default="{ row }">
                {{ row.description || '-' }}
              </template>
            </el-table-column>
            <el-table-column prop="is_active" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="row.is_active ? 'success' : 'danger'">
                  {{ row.is_active ? '启用' : '禁用' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="resident_count" label="住户数量" width="100">
              <template #default="{ row }">
                {{ row.resident_count || 0 }}
              </template>
            </el-table-column>
            <el-table-column prop="created_at" label="创建时间" width="180">
              <template #default="{ row }">
                {{ formatDate(row.created_at) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <el-button
                  type="primary"
                  size="small"
                  @click="showEditDialog(row)"
                >
                  编辑
                </el-button>
                <el-button
                  type="danger"
                  size="small"
                  @click="handleDelete(row)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 新增/编辑对话框 -->
        <el-dialog
          v-model="dialogVisible"
          :title="isEdit ? '编辑部门' : '新增部门'"
          width="500px"
          @close="resetForm"
        >
          <el-form
            ref="formRef"
            :model="formData"
            :rules="formRules"
            label-width="80px"
          >
            <el-form-item label="部门名称" prop="name">
              <el-input
                v-model="formData.name"
                placeholder="请输入部门名称"
                maxlength="50"
                show-word-limit
              />
            </el-form-item>
            <el-form-item label="部门描述" prop="description">
              <el-input
                v-model="formData.description"
                type="textarea"
                :rows="3"
                placeholder="请输入部门描述"
                maxlength="200"
                show-word-limit
              />
            </el-form-item>
            <el-form-item label="状态" prop="is_active">
              <el-switch
                v-model="formData.is_active"
                active-text="启用"
                inactive-text="禁用"
              />
            </el-form-item>
          </el-form>
          
          <template #footer>
            <el-button @click="dialogVisible = false">取消</el-button>
            <el-button
              type="primary"
              :loading="submitLoading"
              @click="handleSubmit"
            >
              {{ isEdit ? '更新' : '创建' }}
            </el-button>
          </template>
        </el-dialog>
      </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue'
import { ElMessageBox } from 'element-plus'
import {
  Plus,
  Refresh,
  Search,
  RefreshLeft
} from '@element-plus/icons-vue'
import dayjs from 'dayjs'

import { useDepartmentStore } from '@/stores/departments'

// 使用store
const departmentStore = useDepartmentStore()

// 响应式数据
const dialogVisible = ref(false)
const isEdit = ref(false)
const submitLoading = ref(false)
const formRef = ref()

// 搜索表单
const searchForm = ref({
  name: '',
  is_active: null
})

// 表单数据
const formData = ref({
  name: '',
  description: '',
  is_active: true
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入部门名称', trigger: 'blur' },
    { min: 2, max: 50, message: '部门名称长度在 2 到 50 个字符', trigger: 'blur' }
  ]
}

// 计算属性
const filteredDepartments = computed(() => {
  let departments = departmentStore.departments
  
  if (searchForm.value.name) {
    departments = departments.filter(dept =>
      dept.name.toLowerCase().includes(searchForm.value.name.toLowerCase())
    )
  }
  
  if (searchForm.value.is_active !== null) {
    departments = departments.filter(dept =>
      dept.is_active === searchForm.value.is_active
    )
  }
  
  return departments
})

// 方法
const refreshData = async () => {
  await departmentStore.fetchDepartments()
}

const showCreateDialog = () => {
  isEdit.value = false
  dialogVisible.value = true
}

const showEditDialog = (row) => {
  isEdit.value = true
  formData.value = { ...row }
  dialogVisible.value = true
}

const resetForm = () => {
  formData.value = {
    name: '',
    description: '',
    is_active: true
  }
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

const handleSearch = () => {
  // 搜索逻辑已在计算属性中实现
}

const resetSearch = () => {
  searchForm.value = {
    name: '',
    is_active: null
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    submitLoading.value = true
    
    if (isEdit.value) {
      await departmentStore.updateDepartment(formData.value.id, formData.value)
    } else {
      await departmentStore.createDepartment(formData.value)
    }
    
    dialogVisible.value = false
    resetForm()
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    submitLoading.value = false
  }
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除部门"${row.name}"吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await departmentStore.deleteDepartment(row.id)
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
    }
  }
}

const formatDate = (dateString) => {
  if (!dateString) return '-'
  return dayjs(dateString).format('YYYY-MM-DD HH:mm:ss')
}

// 生命周期
onMounted(() => {
  refreshData()
})
</script>

<style lang="scss" scoped>
.departments {
  .app-card {
    margin-bottom: 20px;
  }
}
</style>
