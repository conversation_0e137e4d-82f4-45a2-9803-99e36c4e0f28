<template>
  <div class="residents">
    <div class="app-container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1 class="page-title">住户管理</h1>
        <div class="page-actions">
          <el-button type="primary" @click="showCreateDialog">
            <el-icon><Plus /></el-icon>
            新增住户
          </el-button>
          <el-button @click="refreshData">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>

      <!-- 搜索筛选 -->
      <div class="app-card">
        <el-form :model="searchForm" inline>
          <el-form-item label="搜索关键词">
            <el-input
              v-model="searchForm.keyword"
              placeholder="姓名、员工号、电话、邮箱"
              clearable
              style="width: 250px"
            />
          </el-form-item>
          <el-form-item label="所属部门">
            <el-select
              v-model="searchForm.department_id"
              placeholder="请选择部门"
              clearable
              style="width: 200px"
            >
              <el-option
                v-for="dept in departmentStore.departments"
                :key="dept.id"
                :label="dept.name"
                :value="dept.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select
              v-model="searchForm.is_active"
              placeholder="请选择状态"
              clearable
              style="width: 120px"
            >
              <el-option label="启用" :value="true" />
              <el-option label="禁用" :value="false" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="resetSearch">
              <el-icon><RefreshLeft /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 数据表格 -->
      <div class="app-card">
        <el-table
          v-loading="residentStore.loading"
          :data="filteredResidents"
          stripe
          style="width: 100%"
        >
          <el-table-column prop="name" label="姓名" min-width="120" />
          <el-table-column prop="employee_id" label="员工号" width="120">
            <template #default="{ row }">
              {{ row.employee_id || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="phone" label="电话" width="130">
            <template #default="{ row }">
              {{ row.phone || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="email" label="邮箱" min-width="180">
            <template #default="{ row }">
              {{ row.email || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="department_name" label="所属部门" width="150">
            <template #default="{ row }">
              {{ row.department_name || '-' }}
            </template>
          </el-table-column>
          <el-table-column label="当前住宿" width="200">
            <template #default="{ row }">
              <div v-if="row.current_dormitory">
                <div>{{ row.current_dormitory }}</div>
                <div class="text-secondary">{{ row.current_bed_number }}号床</div>
              </div>
              <el-tag v-else type="info" size="small">未入住</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="is_active" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="row.is_active ? 'success' : 'danger'">
                {{ row.is_active ? '启用' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="250" fixed="right">
            <template #default="{ row }">
              <el-button
                type="info"
                size="small"
                @click="showResidenceHistory(row)"
              >
                住宿记录
              </el-button>
              <el-button
                type="primary"
                size="small"
                @click="showEditDialog(row)"
              >
                编辑
              </el-button>
              <el-button
                type="danger"
                size="small"
                @click="handleDelete(row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 新增/编辑对话框 -->
      <el-dialog
        v-model="dialogVisible"
        :title="isEdit ? '编辑住户' : '新增住户'"
        width="600px"
        @close="resetForm"
      >
        <el-form
          ref="formRef"
          :model="formData"
          :rules="formRules"
          label-width="100px"
        >
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="姓名" prop="name">
                <el-input
                  v-model="formData.name"
                  placeholder="请输入姓名"
                  maxlength="50"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="员工号" prop="employee_id">
                <el-input
                  v-model="formData.employee_id"
                  placeholder="请输入员工号"
                  maxlength="20"
                />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="电话号码" prop="phone">
                <el-input
                  v-model="formData.phone"
                  placeholder="请输入电话号码"
                  maxlength="20"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="邮箱地址" prop="email">
                <el-input
                  v-model="formData.email"
                  placeholder="请输入邮箱地址"
                  maxlength="100"
                />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="所属部门" prop="department_id">
                <el-select
                  v-model="formData.department_id"
                  placeholder="请选择部门"
                  style="width: 100%"
                >
                  <el-option
                    v-for="dept in departmentStore.departments"
                    :key="dept.id"
                    :label="dept.name"
                    :value="dept.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="状态" prop="is_active">
                <el-switch
                  v-model="formData.is_active"
                  active-text="启用"
                  inactive-text="禁用"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        
        <template #footer>
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            :loading="submitLoading"
            @click="handleSubmit"
          >
            {{ isEdit ? '更新' : '创建' }}
          </el-button>
        </template>
      </el-dialog>

      <!-- 住宿记录对话框 -->
      <el-dialog
        v-model="historyVisible"
        title="住宿记录"
        width="800px"
      >
        <div v-if="currentResidentHistory">
          <h4>{{ currentResidentHistory.name }} 的住宿记录</h4>
          <el-table
            :data="residenceHistory"
            stripe
            style="width: 100%"
          >
            <el-table-column prop="dormitory_name" label="宿舍" width="120" />
            <el-table-column prop="bed_number" label="床位号" width="80" />
            <el-table-column prop="check_in_date" label="入住日期" width="120" />
            <el-table-column prop="check_out_date" label="离开日期" width="120">
              <template #default="{ row }">
                {{ row.check_out_date || '未离开' }}
              </template>
            </el-table-column>
            <el-table-column prop="days_stayed" label="住宿天数" width="100" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag
                  :type="row.status === 'ACTIVE' ? 'success' : 
                         row.status === 'COMPLETED' ? 'info' : 'warning'"
                >
                  {{ getStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="notes" label="备注" min-width="150">
              <template #default="{ row }">
                {{ row.notes || '-' }}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessageBox } from 'element-plus'
import {
  Plus,
  Refresh,
  Search,
  RefreshLeft
} from '@element-plus/icons-vue'

import { useResidentStore } from '@/stores/residents'
import { useDepartmentStore } from '@/stores/departments'
import { useRecordStore } from '@/stores/records'

// 使用stores
const residentStore = useResidentStore()
const departmentStore = useDepartmentStore()
const recordStore = useRecordStore()

// 响应式数据
const dialogVisible = ref(false)
const historyVisible = ref(false)
const isEdit = ref(false)
const submitLoading = ref(false)
const formRef = ref()
const currentResidentHistory = ref(null)
const residenceHistory = ref([])

// 搜索表单
const searchForm = ref({
  keyword: '',
  department_id: null,
  is_active: null
})

// 表单数据
const formData = ref({
  name: '',
  employee_id: '',
  phone: '',
  email: '',
  department_id: '',
  is_active: true
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { min: 2, max: 50, message: '姓名长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  department_id: [
    { required: true, message: '请选择所属部门', trigger: 'change' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ]
}

// 计算属性
const filteredResidents = computed(() => {
  let residents = residentStore.residents
  
  if (searchForm.value.keyword) {
    const keyword = searchForm.value.keyword.toLowerCase()
    residents = residents.filter(resident =>
      resident.name.toLowerCase().includes(keyword) ||
      (resident.employee_id && resident.employee_id.toLowerCase().includes(keyword)) ||
      (resident.phone && resident.phone.includes(keyword)) ||
      (resident.email && resident.email.toLowerCase().includes(keyword))
    )
  }
  
  if (searchForm.value.department_id) {
    residents = residents.filter(resident =>
      resident.department_id === searchForm.value.department_id
    )
  }
  
  if (searchForm.value.is_active !== null) {
    residents = residents.filter(resident =>
      resident.is_active === searchForm.value.is_active
    )
  }
  
  return residents
})

// 方法
const refreshData = async () => {
  await Promise.all([
    residentStore.fetchResidents(),
    departmentStore.fetchDepartments()
  ])
}

const showCreateDialog = () => {
  isEdit.value = false
  dialogVisible.value = true
}

const showEditDialog = (row) => {
  isEdit.value = true
  formData.value = { ...row }
  dialogVisible.value = true
}

const resetForm = () => {
  formData.value = {
    name: '',
    employee_id: '',
    phone: '',
    email: '',
    department_id: '',
    is_active: true
  }
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

const handleSearch = () => {
  // 搜索逻辑已在计算属性中实现
}

const resetSearch = () => {
  searchForm.value = {
    keyword: '',
    department_id: null,
    is_active: null
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    submitLoading.value = true
    
    if (isEdit.value) {
      await residentStore.updateResident(formData.value.id, formData.value)
    } else {
      await residentStore.createResident(formData.value)
    }
    
    dialogVisible.value = false
    resetForm()
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    submitLoading.value = false
  }
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除住户"${row.name}"吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await residentStore.deleteResident(row.id)
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
    }
  }
}

const showResidenceHistory = async (row) => {
  try {
    currentResidentHistory.value = row
    residenceHistory.value = await recordStore.getRecordsByResident(row.id)
    historyVisible.value = true
  } catch (error) {
    console.error('获取住宿记录失败:', error)
  }
}

const getStatusText = (status) => {
  const statusMap = {
    'ACTIVE': '入住中',
    'COMPLETED': '已离开',
    'CANCELLED': '已取消'
  }
  return statusMap[status] || status
}

// 生命周期
onMounted(() => {
  refreshData()
})
</script>

<style lang="scss" scoped>
.residents {
  .app-card {
    margin-bottom: 20px;
  }
  
  .text-secondary {
    color: var(--el-text-color-secondary);
    font-size: 12px;
  }
}
</style>
