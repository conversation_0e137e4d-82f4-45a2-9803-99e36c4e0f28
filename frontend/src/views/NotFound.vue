<template>
  <div class="not-found">
    <div class="not-found-container">
      <div class="not-found-content">
        <div class="error-code">404</div>
        <div class="error-title">页面不存在</div>
        <div class="error-description">
          抱歉，您访问的页面不存在或已被删除
        </div>
        <div class="error-actions">
          <el-button type="primary" @click="goHome">
            <el-icon><House /></el-icon>
            返回首页
          </el-button>
          <el-button @click="goBack">
            <el-icon><Back /></el-icon>
            返回上页
          </el-button>
        </div>
      </div>
      <div class="error-image">
        <el-icon size="200" color="#e6e8eb">
          <Warning />
        </el-icon>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { House, Back, Warning } from '@element-plus/icons-vue'

const router = useRouter()

const goHome = () => {
  router.push('/reports')
}

const goBack = () => {
  router.go(-1)
}
</script>

<style lang="scss" scoped>
.not-found {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  
  .not-found-container {
    display: flex;
    align-items: center;
    gap: 60px;
    max-width: 800px;
    padding: 40px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  }
  
  .not-found-content {
    flex: 1;
    
    .error-code {
      font-size: 120px;
      font-weight: 700;
      color: #409eff;
      line-height: 1;
      margin-bottom: 20px;
    }
    
    .error-title {
      font-size: 32px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 16px;
    }
    
    .error-description {
      font-size: 16px;
      color: #606266;
      margin-bottom: 32px;
      line-height: 1.6;
    }
    
    .error-actions {
      display: flex;
      gap: 16px;
    }
  }
  
  .error-image {
    flex-shrink: 0;
  }
}

@media (max-width: 768px) {
  .not-found {
    .not-found-container {
      flex-direction: column;
      text-align: center;
      gap: 40px;
      margin: 20px;
      padding: 30px 20px;
    }
    
    .not-found-content {
      .error-code {
        font-size: 80px;
      }
      
      .error-title {
        font-size: 24px;
      }
      
      .error-actions {
        justify-content: center;
      }
    }
    
    .error-image {
      .el-icon {
        font-size: 120px !important;
      }
    }
  }
}
</style>
