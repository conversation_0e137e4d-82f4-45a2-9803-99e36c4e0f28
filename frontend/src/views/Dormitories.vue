<template>
  <div class="dormitories">
    <div class="app-container">
        <!-- 页面标题 -->
        <div class="page-header">
          <h1 class="page-title">宿舍管理</h1>
          <div class="page-actions">
            <el-button type="primary" @click="showCreateDialog">
              <el-icon><Plus /></el-icon>
              新增宿舍
            </el-button>
            <el-button @click="refreshData">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>

        <!-- 搜索筛选 -->
        <div class="app-card">
          <el-form :model="searchForm" inline>
            <el-form-item label="宿舍名称">
              <el-input
                v-model="searchForm.name"
                placeholder="请输入宿舍名称"
                clearable
                style="width: 200px"
              />
            </el-form-item>
            <el-form-item label="状态">
              <el-select
                v-model="searchForm.is_active"
                placeholder="请选择状态"
                clearable
                style="width: 120px"
              >
                <el-option label="启用" :value="true" />
                <el-option label="禁用" :value="false" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">
                <el-icon><Search /></el-icon>
                搜索
              </el-button>
              <el-button @click="resetSearch">
                <el-icon><RefreshLeft /></el-icon>
                重置
              </el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 数据表格 -->
        <div class="app-card">
          <el-table
            v-loading="dormitoryStore.loading"
            :data="filteredDormitories"
            stripe
            style="width: 100%"
          >
            <el-table-column prop="name" label="宿舍名称" min-width="150" />
            <el-table-column prop="department_name" label="所属部门" min-width="120">
              <template #default="{ row }">
                {{ row.department_name || '未分配' }}
              </template>
            </el-table-column>
            <el-table-column prop="total_beds" label="总床位" width="100" />
            <el-table-column prop="occupied_beds" label="已占用" width="100">
              <template #default="{ row }">
                {{ row.occupied_beds || 0 }}
              </template>
            </el-table-column>
            <el-table-column prop="available_beds" label="可用床位" width="100">
              <template #default="{ row }">
                {{ row.available_beds || row.total_beds }}
              </template>
            </el-table-column>
            <el-table-column label="入住率" width="100">
              <template #default="{ row }">
                <el-progress
                  :percentage="getOccupancyRate(row)"
                  :color="getProgressColor(getOccupancyRate(row))"
                  :stroke-width="8"
                />
              </template>
            </el-table-column>
            <el-table-column prop="description" label="描述" min-width="200">
              <template #default="{ row }">
                {{ row.description || '-' }}
              </template>
            </el-table-column>
            <el-table-column prop="is_active" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="row.is_active ? 'success' : 'danger'">
                  {{ row.is_active ? '启用' : '禁用' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="250" fixed="right">
              <template #default="{ row }">
                <el-button
                  type="info"
                  size="small"
                  @click="showBedStatus(row)"
                >
                  床位状态
                </el-button>
                <el-button
                  type="primary"
                  size="small"
                  @click="showEditDialog(row)"
                >
                  编辑
                </el-button>
                <el-button
                  type="danger"
                  size="small"
                  @click="handleDelete(row)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 新增/编辑对话框 -->
        <el-dialog
          v-model="dialogVisible"
          :title="isEdit ? '编辑宿舍' : '新增宿舍'"
          width="500px"
          @close="resetForm"
        >
          <el-form
            ref="formRef"
            :model="formData"
            :rules="formRules"
            label-width="80px"
          >
            <el-form-item label="宿舍名称" prop="name">
              <el-input
                v-model="formData.name"
                placeholder="请输入宿舍名称"
                maxlength="50"
                show-word-limit
              />
            </el-form-item>
            <el-form-item label="总床位数" prop="total_beds">
              <el-input-number
                v-model="formData.total_beds"
                :min="1"
                :max="20"
                placeholder="请输入总床位数"
                style="width: 100%"
              />
            </el-form-item>
            <el-form-item label="所属部门" prop="department_id">
              <el-select
                v-model="formData.department_id"
                placeholder="请选择所属部门"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="dept in departments"
                  :key="dept.id"
                  :label="dept.name"
                  :value="dept.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="宿舍描述" prop="description">
              <el-input
                v-model="formData.description"
                type="textarea"
                :rows="3"
                placeholder="请输入宿舍描述"
                maxlength="200"
                show-word-limit
              />
            </el-form-item>
            <el-form-item label="状态" prop="is_active">
              <el-switch
                v-model="formData.is_active"
                active-text="启用"
                inactive-text="禁用"
              />
            </el-form-item>
          </el-form>
          
          <template #footer>
            <el-button @click="dialogVisible = false">取消</el-button>
            <el-button
              type="primary"
              :loading="submitLoading"
              @click="handleSubmit"
            >
              {{ isEdit ? '更新' : '创建' }}
            </el-button>
          </template>
        </el-dialog>

        <!-- 床位状态对话框 -->
        <el-dialog
          v-model="bedStatusVisible"
          title="床位状态"
          width="600px"
        >
          <div v-if="currentBedStatus">
            <div class="bed-status-header">
              <h3>{{ currentBedStatus.dormitory_name }}</h3>
              <p>总床位: {{ currentBedStatus.total_beds }} | 
                 已占用: {{ currentBedStatus.occupied_beds }} | 
                 可用: {{ currentBedStatus.available_beds }}</p>
            </div>
            
            <div class="bed-grid">
              <div
                v-for="bed in currentBedStatus.beds"
                :key="bed.bed_number"
                :class="['bed-item', bed.is_occupied ? 'occupied' : 'available']"
              >
                <div class="bed-number">{{ bed.bed_number }}</div>
                <div class="bed-status">
                  {{ bed.is_occupied ? '已占用' : '空闲' }}
                </div>
                <div v-if="bed.resident_name" class="resident-name">
                  {{ bed.resident_name }}
                </div>
              </div>
            </div>
          </div>
        </el-dialog>
      </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessageBox } from 'element-plus'
import {
  Plus,
  Refresh,
  Search,
  RefreshLeft
} from '@element-plus/icons-vue'

import { useDormitoryStore } from '@/stores/dormitories'
import { useDepartmentStore } from '@/stores/departments'

// 使用store
const dormitoryStore = useDormitoryStore()
const departmentStore = useDepartmentStore()

// 响应式数据
const dialogVisible = ref(false)
const bedStatusVisible = ref(false)
const isEdit = ref(false)
const submitLoading = ref(false)
const formRef = ref()
const currentBedStatus = ref(null)

// 搜索表单
const searchForm = ref({
  name: '',
  is_active: null
})

// 表单数据
const formData = ref({
  name: '',
  total_beds: 1,
  description: '',
  department_id: null,
  is_active: true
})

// 部门列表
const departments = computed(() => departmentStore.activeDepartments)

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入宿舍名称', trigger: 'blur' },
    { min: 2, max: 50, message: '宿舍名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  total_beds: [
    { required: true, message: '请输入总床位数', trigger: 'blur' },
    { type: 'number', min: 1, max: 20, message: '床位数必须在 1 到 20 之间', trigger: 'blur' }
  ]
}

// 计算属性
const filteredDormitories = computed(() => {
  let dormitories = dormitoryStore.dormitories
  
  if (searchForm.value.name) {
    dormitories = dormitories.filter(dorm =>
      dorm.name.toLowerCase().includes(searchForm.value.name.toLowerCase())
    )
  }
  
  if (searchForm.value.is_active !== null) {
    dormitories = dormitories.filter(dorm =>
      dorm.is_active === searchForm.value.is_active
    )
  }
  
  return dormitories
})

// 方法
const refreshData = async () => {
  await dormitoryStore.fetchDormitories()
}

const showCreateDialog = () => {
  isEdit.value = false
  dialogVisible.value = true
}

const showEditDialog = (row) => {
  isEdit.value = true
  formData.value = { ...row }
  dialogVisible.value = true
}

const resetForm = () => {
  formData.value = {
    name: '',
    total_beds: 1,
    description: '',
    department_id: null,
    is_active: true
  }
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

const handleSearch = () => {
  // 搜索逻辑已在计算属性中实现
}

const resetSearch = () => {
  searchForm.value = {
    name: '',
    is_active: null
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    submitLoading.value = true
    
    if (isEdit.value) {
      await dormitoryStore.updateDormitory(formData.value.id, formData.value)
    } else {
      await dormitoryStore.createDormitory(formData.value)
    }
    
    dialogVisible.value = false
    resetForm()
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    submitLoading.value = false
  }
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除宿舍"${row.name}"吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await dormitoryStore.deleteDormitory(row.id)
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
    }
  }
}

const showBedStatus = async (row) => {
  try {
    const status = await dormitoryStore.fetchBedStatus(row.id)
    currentBedStatus.value = status
    bedStatusVisible.value = true
  } catch (error) {
    console.error('获取床位状态失败:', error)
  }
}

const getOccupancyRate = (row) => {
  const occupied = row.occupied_beds || 0
  const total = row.total_beds || 1
  return Math.round((occupied / total) * 100)
}

const getProgressColor = (percentage) => {
  if (percentage < 50) return '#67c23a'
  if (percentage < 80) return '#e6a23c'
  return '#f56c6c'
}

// 生命周期
onMounted(() => {
  refreshData()
  departmentStore.fetchDepartments()
})
</script>

<style lang="scss" scoped>
.dormitories {
  .app-card {
    margin-bottom: 20px;
  }
  
  .bed-status-header {
    margin-bottom: 20px;
    text-align: center;
    
    h3 {
      margin: 0 0 10px 0;
      color: var(--el-text-color-primary);
    }
    
    p {
      margin: 0;
      color: var(--el-text-color-secondary);
    }
  }
  
  .bed-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 16px;
    
    .bed-item {
      padding: 16px;
      border-radius: 8px;
      text-align: center;
      border: 2px solid;
      transition: all 0.3s;
      
      &.available {
        border-color: #67c23a;
        background-color: #f0f9ff;
        color: #67c23a;
      }
      
      &.occupied {
        border-color: #f56c6c;
        background-color: #fef0f0;
        color: #f56c6c;
      }
      
      .bed-number {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 8px;
      }
      
      .bed-status {
        font-size: 12px;
        margin-bottom: 4px;
      }
      
      .resident-name {
        font-size: 12px;
        opacity: 0.8;
      }
    }
  }
}
</style>
