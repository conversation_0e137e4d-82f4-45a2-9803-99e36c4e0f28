import request from './request'

/**
 * 报表统计API
 */
export const reportApi = {
  /**
   * 获取实时报告
   */
  getRealtimeReport() {
    return request({
      url: '/v1/reports/realtime',
      method: 'GET'
    })
  },

  /**
   * 获取月度报告
   * @param {number} year - 年份
   * @param {number} month - 月份
   */
  getMonthlyReport(year, month) {
    return request({
      url: `/v1/reports/monthly/${year}/${month}`,
      method: 'GET'
    })
  },

  /**
   * 获取日度分摊明细
   * @param {Object} params - 查询参数
   * @param {string} params.start_date - 开始日期
   * @param {string} params.end_date - 结束日期
   * @param {string} params.department_id - 部门ID筛选
   * @param {string} params.dormitory_id - 宿舍ID筛选
   */
  getDailyAllocations(params = {}) {
    return request({
      url: '/v1/reports/daily',
      method: 'GET',
      params
    })
  },

  /**
   * 获取年度汇总
   * @param {number} year - 年份
   */
  getYearlySummary(year) {
    return request({
      url: `/v1/reports/summary/${year}`,
      method: 'GET'
    })
  },

  /**
   * 导出月度报告
   * @param {number} year - 年份
   * @param {number} month - 月份
   * @param {string} format - 导出格式 (excel, pdf, csv)
   */
  exportMonthlyReport(year, month, format = 'excel') {
    return request({
      url: `/v1/reports/monthly/${year}/${month}/export`,
      method: 'GET',
      params: { format },
      responseType: 'blob'
    })
  },

  /**
   * 导出年度汇总
   * @param {number} year - 年份
   * @param {string} format - 导出格式 (excel, pdf, csv)
   */
  exportYearlySummary(year, format = 'excel') {
    return request({
      url: `/v1/reports/summary/${year}/export`,
      method: 'GET',
      params: { format },
      responseType: 'blob'
    })
  }
}
