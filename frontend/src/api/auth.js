import request from './request'

/**
 * 认证相关API
 */

// 用户登录
export const login = (data) => {
  return request({
    url: '/v1/auth/login',
    method: 'post',
    data,
    loading: true
  })
}

// 获取当前用户信息
export const getCurrentUser = () => {
  return request({
    url: '/v1/auth/me',
    method: 'get',
    loading: false
  })
}

// 验证Token
export const verifyToken = () => {
  return request({
    url: '/v1/auth/verify',
    method: 'get',
    loading: false
  })
}

// 用户登出
export const logout = () => {
  return request({
    url: '/v1/auth/logout',
    method: 'post',
    loading: false
  })
}

// 检查用户是否已登录
export const isAuthenticated = () => {
  const token = localStorage.getItem('token')
  return !!token
}

// 清除认证信息
export const clearAuth = () => {
  localStorage.removeItem('token')
  localStorage.removeItem('userInfo')
}

// 保存认证信息
export const saveAuth = (token, userInfo) => {
  localStorage.setItem('token', token)
  if (userInfo) {
    localStorage.setItem('userInfo', JSON.stringify(userInfo))
  }
}

// 获取用户信息
export const getUserInfo = () => {
  const userInfo = localStorage.getItem('userInfo')
  return userInfo ? JSON.parse(userInfo) : null
}
