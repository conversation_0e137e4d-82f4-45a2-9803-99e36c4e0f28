import request from './request'

/**
 * 住户管理API
 */
export const residentApi = {
  /**
   * 获取住户列表
   * @param {Object} params - 查询参数
   * @param {number} params.skip - 跳过记录数
   * @param {number} params.limit - 限制记录数
   * @param {string} params.department_id - 部门ID筛选
   * @param {boolean} params.is_active - 是否启用筛选
   * @param {string} params.keyword - 搜索关键词
   */
  getResidents(params = {}) {
    return request({
      url: '/v1/residents/',
      method: 'GET',
      params
    })
  },

  /**
   * 创建住户
   * @param {Object} data - 住户数据
   * @param {string} data.name - 姓名
   * @param {string} data.employee_id - 员工号
   * @param {string} data.phone - 电话号码
   * @param {string} data.email - 邮箱地址
   * @param {string} data.department_id - 部门ID
   * @param {boolean} data.is_active - 是否启用
   */
  createResident(data) {
    return request({
      url: '/v1/residents/',
      method: 'POST',
      data
    })
  },

  /**
   * 获取住户详情
   * @param {string} id - 住户ID
   */
  getResident(id) {
    return request({
      url: `/v1/residents/${id}`,
      method: 'GET'
    })
  },

  /**
   * 更新住户
   * @param {string} id - 住户ID
   * @param {Object} data - 更新数据
   */
  updateResident(id, data) {
    return request({
      url: `/v1/residents/${id}`,
      method: 'PUT',
      data
    })
  },

  /**
   * 删除住户
   * @param {string} id - 住户ID
   */
  deleteResident(id) {
    return request({
      url: `/v1/residents/${id}`,
      method: 'DELETE'
    })
  },

  /**
   * 根据部门获取住户列表
   * @param {string} departmentId - 部门ID
   */
  getResidentsByDepartment(departmentId) {
    return request({
      url: `/v1/residents/department/${departmentId}`,
      method: 'GET'
    })
  },

  /**
   * 获取住户当前住宿情况
   * @param {string} id - 住户ID
   */
  getCurrentResidence(id) {
    return request({
      url: `/v1/residents/${id}/current-residence`,
      method: 'GET'
    })
  }
}
