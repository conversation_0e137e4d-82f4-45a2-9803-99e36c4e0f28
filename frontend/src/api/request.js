import axios from 'axios'
import { ElMessage, ElLoading } from 'element-plus'

// 创建axios实例
const request = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
let loadingInstance = null
request.interceptors.request.use(
  (config) => {
    // 显示加载动画
    if (config.loading !== false) {
      loadingInstance = ElLoading.service({
        lock: true,
        text: '加载中...',
        background: 'rgba(0, 0, 0, 0.7)'
      })
    }
    
    // 添加认证token（如果有）
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    return config
  },
  (error) => {
    if (loadingInstance) {
      loadingInstance.close()
    }
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response) => {
    if (loadingInstance) {
      loadingInstance.close()
    }
    
    return response.data
  },
  (error) => {
    if (loadingInstance) {
      loadingInstance.close()
    }
    
    // 处理错误响应
    const { response } = error
    let message = '请求失败'
    
    if (response) {
      switch (response.status) {
        case 400:
          message = response.data?.detail || '请求参数错误'
          break
        case 401:
          message = '登录已过期，请重新登录'
          // 清除token并跳转到登录页
          localStorage.removeItem('token')
          localStorage.removeItem('userInfo')
          // 使用路由跳转而不是直接修改location
          setTimeout(() => {
            window.location.href = '/login'
          }, 1000)
          break
        case 403:
          message = '拒绝访问'
          break
        case 404:
          message = '请求的资源不存在'
          break
        case 422:
          message = response.data?.detail || '数据验证失败'
          break
        case 500:
          message = response.data?.detail || '服务器内部错误'
          break
        default:
          message = `请求失败 (${response.status})`
      }
    } else if (error.code === 'ECONNABORTED') {
      message = '请求超时'
    } else if (error.message === 'Network Error') {
      message = '网络连接失败'
    }
    
    ElMessage.error(message)
    return Promise.reject(error)
  }
)

export default request
