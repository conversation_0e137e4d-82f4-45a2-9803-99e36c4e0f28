import request from './request'

/**
 * 部门管理API
 */
export const departmentApi = {
  /**
   * 获取部门列表
   * @param {Object} params - 查询参数
   * @param {number} params.skip - 跳过记录数
   * @param {number} params.limit - 限制记录数
   * @param {boolean} params.is_active - 是否启用筛选
   */
  getDepartments(params = {}) {
    return request({
      url: '/v1/departments/',
      method: 'GET',
      params
    })
  },

  /**
   * 创建部门
   * @param {Object} data - 部门数据
   * @param {string} data.name - 部门名称
   * @param {string} data.description - 部门描述
   * @param {boolean} data.is_active - 是否启用
   */
  createDepartment(data) {
    return request({
      url: '/v1/departments/',
      method: 'POST',
      data
    })
  },

  /**
   * 获取部门详情
   * @param {string} id - 部门ID
   */
  getDepartment(id) {
    return request({
      url: `/v1/departments/${id}`,
      method: 'GET'
    })
  },

  /**
   * 更新部门
   * @param {string} id - 部门ID
   * @param {Object} data - 更新数据
   */
  updateDepartment(id, data) {
    return request({
      url: `/v1/departments/${id}`,
      method: 'PUT',
      data
    })
  },

  /**
   * 删除部门
   * @param {string} id - 部门ID
   */
  deleteDepartment(id) {
    return request({
      url: `/v1/departments/${id}`,
      method: 'DELETE'
    })
  }
}
