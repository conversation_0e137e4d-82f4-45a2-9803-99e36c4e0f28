import request from './request'

/**
 * 入住记录管理API
 */
export const recordApi = {
  /**
   * 获取入住记录列表
   * @param {Object} params - 查询参数
   * @param {number} params.skip - 跳过记录数
   * @param {number} params.limit - 限制记录数
   * @param {string} params.dormitory_id - 宿舍ID筛选
   * @param {string} params.department_id - 部门ID筛选
   * @param {string} params.status - 状态筛选
   * @param {string} params.check_in_start - 入住开始日期
   * @param {string} params.check_in_end - 入住结束日期
   */
  getRecords(params = {}) {
    return request({
      url: '/v1/records/',
      method: 'GET',
      params
    })
  },

  /**
   * 创建入住记录
   * @param {Object} data - 入住记录数据
   * @param {string} data.resident_id - 住户ID
   * @param {string} data.dormitory_id - 宿舍ID
   * @param {number} data.bed_number - 床位号
   * @param {string} data.check_in_date - 入住日期
   * @param {string} data.notes - 备注
   */
  createRecord(data) {
    return request({
      url: '/v1/records/',
      method: 'POST',
      data
    })
  },

  /**
   * 获取所有活跃的入住记录
   */
  getActiveRecords() {
    return request({
      url: '/v1/records/active',
      method: 'GET'
    })
  },

  /**
   * 获取入住记录详情
   * @param {string} id - 记录ID
   */
  getRecord(id) {
    return request({
      url: `/v1/records/${id}`,
      method: 'GET'
    })
  },

  /**
   * 更新入住记录
   * @param {string} id - 记录ID
   * @param {Object} data - 更新数据
   */
  updateRecord(id, data) {
    return request({
      url: `/v1/records/${id}`,
      method: 'PUT',
      data
    })
  },

  /**
   * 删除入住记录
   * @param {string} id - 记录ID
   */
  deleteRecord(id) {
    return request({
      url: `/v1/records/${id}`,
      method: 'DELETE'
    })
  },

  /**
   * 办理住户离开
   * @param {string} id - 记录ID
   * @param {string} checkoutDate - 离开日期
   * @param {string} notes - 备注
   */
  checkoutResident(id, checkoutDate, notes = null) {
    return request({
      url: `/v1/records/${id}/checkout`,
      method: 'POST',
      params: {
        checkout_date: checkoutDate,
        notes
      }
    })
  },

  /**
   * 获取住户的所有入住记录
   * @param {string} residentId - 住户ID
   */
  getRecordsByResident(residentId) {
    return request({
      url: `/v1/records/resident/${residentId}`,
      method: 'GET'
    })
  },

  /**
   * 获取宿舍的所有入住记录
   * @param {string} dormitoryId - 宿舍ID
   * @param {string} status - 状态筛选
   */
  getRecordsByDormitory(dormitoryId, status = null) {
    return request({
      url: `/v1/records/dormitory/${dormitoryId}`,
      method: 'GET',
      params: status ? { status } : {}
    })
  }
}
