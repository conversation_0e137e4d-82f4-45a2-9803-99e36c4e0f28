import request from './request'

/**
 * 宿舍管理API
 */
export const dormitoryApi = {
  /**
   * 获取宿舍列表
   * @param {Object} params - 查询参数
   * @param {number} params.skip - 跳过记录数
   * @param {number} params.limit - 限制记录数
   * @param {boolean} params.is_active - 是否启用筛选
   */
  getDormitories(params = {}) {
    return request({
      url: '/v1/dormitories/',
      method: 'GET',
      params
    })
  },

  /**
   * 创建宿舍
   * @param {Object} data - 宿舍数据
   * @param {string} data.name - 宿舍名称
   * @param {number} data.total_beds - 总床位数
   * @param {string} data.description - 宿舍描述
   * @param {boolean} data.is_active - 是否启用
   */
  createDormitory(data) {
    return request({
      url: '/v1/dormitories/',
      method: 'POST',
      data
    })
  },

  /**
   * 获取宿舍详情
   * @param {string} id - 宿舍ID
   */
  getDormitory(id) {
    return request({
      url: `/v1/dormitories/${id}`,
      method: 'GET'
    })
  },

  /**
   * 更新宿舍
   * @param {string} id - 宿舍ID
   * @param {Object} data - 更新数据
   */
  updateDormitory(id, data) {
    return request({
      url: `/v1/dormitories/${id}`,
      method: 'PUT',
      data
    })
  },

  /**
   * 删除宿舍
   * @param {string} id - 宿舍ID
   */
  deleteDormitory(id) {
    return request({
      url: `/v1/dormitories/${id}`,
      method: 'DELETE'
    })
  },

  /**
   * 获取宿舍床位状态
   * @param {string} id - 宿舍ID
   * @param {string} date - 日期 (YYYY-MM-DD格式)
   */
  getBedStatus(id, date = null) {
    const params = date ? { date } : {}
    return request({
      url: `/v1/dormitories/${id}/bed-status`,
      method: 'GET',
      params
    })
  }
}
