import { createRouter, createWebHistory } from 'vue-router'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import { useAuthStore } from '@/stores/auth'

// 配置NProgress
NProgress.configure({ showSpinner: false })

// 路由配置
const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: {
      title: '用户登录',
      requiresAuth: false
    }
  },
  {
    path: '/',
    redirect: '/reports'
  },
  {
    path: '/',
    component: () => import('@/components/Layout/AppLayout.vue'),
    meta: {
      requiresAuth: true
    },
    children: [
      {
        path: 'reports',
        name: 'Reports',
        component: () => import('@/views/Reports.vue'),
        meta: {
          title: '报表统计',
          icon: 'TrendCharts',
          requiresAuth: true
        }
      },
      {
        path: 'records',
        name: 'Records',
        component: () => import('@/views/Records.vue'),
        meta: {
          title: '入住记录',
          icon: 'Document',
          requiresAuth: true
        }
      },
      {
        path: 'departments',
        name: 'Departments',
        component: () => import('@/views/Departments.vue'),
        meta: {
          title: '部门管理',
          icon: 'OfficeBuilding',
          requiresAuth: true
        }
      },
      {
        path: 'dormitories',
        name: 'Dormitories',
        component: () => import('@/views/Dormitories.vue'),
        meta: {
          title: '宿舍管理',
          icon: 'House',
          requiresAuth: true
        }
      },
      {
        path: 'residents',
        name: 'Residents',
        component: () => import('@/views/Residents.vue'),
        meta: {
          title: '住户管理',
          icon: 'User',
          requiresAuth: true
        }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/NotFound.vue'),
    meta: {
      title: '页面不存在'
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  NProgress.start()

  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 宿舍入住管理系统`
  }

  // 检查是否需要认证
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth !== false)
  const authStore = useAuthStore()

  if (requiresAuth) {
    // 需要认证的页面
    if (!authStore.isAuthenticated) {
      // 未登录，跳转到登录页
      next('/login')
      return
    }

    // 已登录，验证token是否有效
    try {
      const isValid = await authStore.checkAuth()
      if (!isValid) {
        next('/login')
        return
      }
    } catch (error) {
      console.error('认证检查失败:', error)
      next('/login')
      return
    }
  } else {
    // 不需要认证的页面（如登录页）
    if (to.path === '/login' && authStore.isAuthenticated) {
      // 已登录用户访问登录页，重定向到首页
      next('/')
      return
    }
  }

  next()
})

router.afterEach(() => {
  NProgress.done()
})

export default router
