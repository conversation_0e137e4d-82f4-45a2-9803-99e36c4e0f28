import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { departmentApi } from '@/api/departments'
import { ElMessage } from 'element-plus'

export const useDepartmentStore = defineStore('department', () => {
  // 状态
  const departments = ref([])
  const loading = ref(false)
  const currentDepartment = ref(null)

  // 计算属性
  const activeDepartments = computed(() => 
    departments.value.filter(dept => dept.is_active)
  )

  const totalDepartments = computed(() => departments.value.length)

  // 操作方法
  const fetchDepartments = async (params = {}) => {
    loading.value = true
    try {
      const data = await departmentApi.getDepartments(params)
      departments.value = data
      return data
    } catch (error) {
      console.error('获取部门列表失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const createDepartment = async (departmentData) => {
    loading.value = true
    try {
      const newDepartment = await departmentApi.createDepartment(departmentData)
      departments.value.unshift(newDepartment)
      ElMessage.success('部门创建成功')
      return newDepartment
    } catch (error) {
      console.error('创建部门失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const updateDepartment = async (id, updateData) => {
    loading.value = true
    try {
      const updatedDepartment = await departmentApi.updateDepartment(id, updateData)
      const index = departments.value.findIndex(dept => dept.id === id)
      if (index !== -1) {
        departments.value[index] = updatedDepartment
      }
      ElMessage.success('部门更新成功')
      return updatedDepartment
    } catch (error) {
      console.error('更新部门失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const deleteDepartment = async (id) => {
    loading.value = true
    try {
      await departmentApi.deleteDepartment(id)
      departments.value = departments.value.filter(dept => dept.id !== id)
      ElMessage.success('部门删除成功')
    } catch (error) {
      console.error('删除部门失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const getDepartmentById = async (id) => {
    loading.value = true
    try {
      const department = await departmentApi.getDepartment(id)
      currentDepartment.value = department
      return department
    } catch (error) {
      console.error('获取部门详情失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 重置状态
  const resetState = () => {
    departments.value = []
    currentDepartment.value = null
    loading.value = false
  }

  return {
    // 状态
    departments,
    loading,
    currentDepartment,
    
    // 计算属性
    activeDepartments,
    totalDepartments,
    
    // 方法
    fetchDepartments,
    createDepartment,
    updateDepartment,
    deleteDepartment,
    getDepartmentById,
    resetState
  }
})
