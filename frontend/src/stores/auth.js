import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import * as authApi from '@/api/auth'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref(localStorage.getItem('token') || '')
  const userInfo = ref(null)
  const isLoading = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value)
  const username = computed(() => userInfo.value?.username || '')

  // 初始化用户信息
  const initUserInfo = () => {
    const savedUserInfo = localStorage.getItem('userInfo')
    if (savedUserInfo) {
      try {
        userInfo.value = JSON.parse(savedUserInfo)
      } catch (error) {
        console.error('解析用户信息失败:', error)
        localStorage.removeItem('userInfo')
      }
    }
  }

  // 登录
  const login = async (loginData) => {
    try {
      isLoading.value = true
      const response = await authApi.login(loginData)
      
      // 保存认证信息
      token.value = response.access_token
      userInfo.value = response.user_info
      
      // 保存到localStorage
      authApi.saveAuth(response.access_token, response.user_info)
      
      ElMessage.success('登录成功')
      return response
    } catch (error) {
      ElMessage.error(error.response?.data?.detail || '登录失败')
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 登出
  const logout = async () => {
    try {
      // 调用后端登出接口
      if (token.value) {
        await authApi.logout()
      }
    } catch (error) {
      console.error('登出接口调用失败:', error)
    } finally {
      // 清除本地状态
      token.value = ''
      userInfo.value = null
      authApi.clearAuth()
      ElMessage.success('已退出登录')
    }
  }

  // 获取用户信息
  const fetchUserInfo = async () => {
    try {
      if (!token.value) return null
      
      const response = await authApi.getCurrentUser()
      userInfo.value = response
      localStorage.setItem('userInfo', JSON.stringify(response))
      return response
    } catch (error) {
      console.error('获取用户信息失败:', error)
      // 如果获取用户信息失败，可能token已过期
      if (error.response?.status === 401) {
        await logout()
      }
      throw error
    }
  }

  // 验证Token
  const verifyToken = async () => {
    try {
      if (!token.value) return false
      
      const response = await authApi.verifyToken()
      return response.success
    } catch (error) {
      console.error('Token验证失败:', error)
      if (error.response?.status === 401) {
        await logout()
      }
      return false
    }
  }

  // 检查认证状态
  const checkAuth = async () => {
    if (!token.value) return false
    
    try {
      const isValid = await verifyToken()
      if (!isValid) {
        await logout()
        return false
      }
      
      // 如果没有用户信息，尝试获取
      if (!userInfo.value) {
        await fetchUserInfo()
      }
      
      return true
    } catch (error) {
      console.error('检查认证状态失败:', error)
      await logout()
      return false
    }
  }

  // 初始化
  initUserInfo()

  return {
    // 状态
    token,
    userInfo,
    isLoading,
    
    // 计算属性
    isAuthenticated,
    username,
    
    // 方法
    login,
    logout,
    fetchUserInfo,
    verifyToken,
    checkAuth
  }
})
