import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { residentApi } from '@/api/residents'
import { ElMessage } from 'element-plus'

export const useResidentStore = defineStore('resident', () => {
  // 状态
  const residents = ref([])
  const loading = ref(false)
  const currentResident = ref(null)

  // 计算属性
  const activeResidents = computed(() => 
    residents.value.filter(resident => resident.is_active)
  )

  const totalResidents = computed(() => residents.value.length)

  const residentsByDepartment = computed(() => {
    const groups = {}
    residents.value.forEach(resident => {
      const deptName = resident.department_name || '未分配部门'
      if (!groups[deptName]) {
        groups[deptName] = []
      }
      groups[deptName].push(resident)
    })
    return groups
  })

  // 操作方法
  const fetchResidents = async (params = {}) => {
    loading.value = true
    try {
      const data = await residentApi.getResidents(params)
      residents.value = data
      return data
    } catch (error) {
      console.error('获取住户列表失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const createResident = async (residentData) => {
    loading.value = true
    try {
      const newResident = await residentApi.createResident(residentData)
      residents.value.unshift(newResident)
      ElMessage.success('住户创建成功')
      return newResident
    } catch (error) {
      console.error('创建住户失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const updateResident = async (id, updateData) => {
    loading.value = true
    try {
      const updatedResident = await residentApi.updateResident(id, updateData)
      const index = residents.value.findIndex(resident => resident.id === id)
      if (index !== -1) {
        residents.value[index] = updatedResident
      }
      ElMessage.success('住户更新成功')
      return updatedResident
    } catch (error) {
      console.error('更新住户失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const deleteResident = async (id) => {
    loading.value = true
    try {
      await residentApi.deleteResident(id)
      residents.value = residents.value.filter(resident => resident.id !== id)
      ElMessage.success('住户删除成功')
    } catch (error) {
      console.error('删除住户失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const getResidentById = async (id) => {
    loading.value = true
    try {
      const resident = await residentApi.getResident(id)
      currentResident.value = resident
      return resident
    } catch (error) {
      console.error('获取住户详情失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const getResidentsByDepartment = async (departmentId) => {
    loading.value = true
    try {
      const data = await residentApi.getResidentsByDepartment(departmentId)
      return data
    } catch (error) {
      console.error('根据部门获取住户列表失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const getCurrentResidence = async (id) => {
    try {
      const residence = await residentApi.getCurrentResidence(id)
      return residence
    } catch (error) {
      console.error('获取住户当前住宿情况失败:', error)
      throw error
    }
  }

  const searchResidents = async (keyword, departmentId = null) => {
    loading.value = true
    try {
      const params = { keyword }
      if (departmentId) {
        params.department_id = departmentId
      }
      const data = await residentApi.getResidents(params)
      return data
    } catch (error) {
      console.error('搜索住户失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 重置状态
  const resetState = () => {
    residents.value = []
    currentResident.value = null
    loading.value = false
  }

  return {
    // 状态
    residents,
    loading,
    currentResident,
    
    // 计算属性
    activeResidents,
    totalResidents,
    residentsByDepartment,
    
    // 方法
    fetchResidents,
    createResident,
    updateResident,
    deleteResident,
    getResidentById,
    getResidentsByDepartment,
    getCurrentResidence,
    searchResidents,
    resetState
  }
})
