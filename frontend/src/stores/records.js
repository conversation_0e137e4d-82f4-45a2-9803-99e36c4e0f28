import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { recordApi } from '@/api/records'
import { ElMessage } from 'element-plus'

export const useRecordStore = defineStore('record', () => {
  // 状态
  const records = ref([])
  const loading = ref(false)
  const currentRecord = ref(null)
  const activeRecords = ref([])

  // 计算属性
  const totalRecords = computed(() => records.value.length)

  const recordsByStatus = computed(() => {
    const groups = {
      ACTIVE: [],
      COMPLETED: [],
      CANCELLED: []
    }
    records.value.forEach(record => {
      if (groups[record.status]) {
        groups[record.status].push(record)
      }
    })
    return groups
  })

  const recordsByDormitory = computed(() => {
    const groups = {}
    records.value.forEach(record => {
      const dormName = record.dormitory_name || '未知宿舍'
      if (!groups[dormName]) {
        groups[dormName] = []
      }
      groups[dormName].push(record)
    })
    return groups
  })

  // 操作方法
  const fetchRecords = async (params = {}) => {
    loading.value = true
    try {
      const data = await recordApi.getRecords(params)
      records.value = data
      return data
    } catch (error) {
      console.error('获取入住记录列表失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const fetchActiveRecords = async () => {
    loading.value = true
    try {
      const data = await recordApi.getActiveRecords()
      activeRecords.value = data
      return data
    } catch (error) {
      console.error('获取活跃入住记录失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const createRecord = async (recordData) => {
    loading.value = true
    try {
      const newRecord = await recordApi.createRecord(recordData)
      records.value.unshift(newRecord)
      // 如果是活跃记录，也添加到活跃记录列表
      if (newRecord.status === 'ACTIVE') {
        activeRecords.value.unshift(newRecord)
      }
      ElMessage.success('入住记录创建成功')
      return newRecord
    } catch (error) {
      console.error('创建入住记录失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const updateRecord = async (id, updateData) => {
    loading.value = true
    try {
      const updatedRecord = await recordApi.updateRecord(id, updateData)
      
      // 更新主记录列表
      const index = records.value.findIndex(record => record.id === id)
      if (index !== -1) {
        records.value[index] = updatedRecord
      }
      
      // 更新活跃记录列表
      const activeIndex = activeRecords.value.findIndex(record => record.id === id)
      if (activeIndex !== -1) {
        if (updatedRecord.status === 'ACTIVE') {
          activeRecords.value[activeIndex] = updatedRecord
        } else {
          // 如果状态不再是活跃，从活跃列表中移除
          activeRecords.value.splice(activeIndex, 1)
        }
      }
      
      ElMessage.success('入住记录更新成功')
      return updatedRecord
    } catch (error) {
      console.error('更新入住记录失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const deleteRecord = async (id) => {
    loading.value = true
    try {
      await recordApi.deleteRecord(id)
      records.value = records.value.filter(record => record.id !== id)
      activeRecords.value = activeRecords.value.filter(record => record.id !== id)
      ElMessage.success('入住记录删除成功')
    } catch (error) {
      console.error('删除入住记录失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const getRecordById = async (id) => {
    loading.value = true
    try {
      const record = await recordApi.getRecord(id)
      currentRecord.value = record
      return record
    } catch (error) {
      console.error('获取入住记录详情失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const checkoutResident = async (id, checkoutDate, notes = null) => {
    loading.value = true
    try {
      const updatedRecord = await recordApi.checkoutResident(id, checkoutDate, notes)
      
      // 更新记录列表
      const index = records.value.findIndex(record => record.id === id)
      if (index !== -1) {
        records.value[index] = updatedRecord
      }
      
      // 从活跃记录列表中移除
      activeRecords.value = activeRecords.value.filter(record => record.id !== id)
      
      ElMessage.success('办理离开成功')
      return updatedRecord
    } catch (error) {
      console.error('办理离开失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const getRecordsByResident = async (residentId) => {
    loading.value = true
    try {
      const data = await recordApi.getRecordsByResident(residentId)
      return data
    } catch (error) {
      console.error('获取住户入住记录失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const getRecordsByDormitory = async (dormitoryId, status = null) => {
    loading.value = true
    try {
      const data = await recordApi.getRecordsByDormitory(dormitoryId, status)
      return data
    } catch (error) {
      console.error('获取宿舍入住记录失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 重置状态
  const resetState = () => {
    records.value = []
    currentRecord.value = null
    activeRecords.value = []
    loading.value = false
  }

  return {
    // 状态
    records,
    loading,
    currentRecord,
    activeRecords,
    
    // 计算属性
    totalRecords,
    recordsByStatus,
    recordsByDormitory,
    
    // 方法
    fetchRecords,
    fetchActiveRecords,
    createRecord,
    updateRecord,
    deleteRecord,
    getRecordById,
    checkoutResident,
    getRecordsByResident,
    getRecordsByDormitory,
    resetState
  }
})
