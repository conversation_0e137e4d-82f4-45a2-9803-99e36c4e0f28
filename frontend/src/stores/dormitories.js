import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { dormitoryApi } from '@/api/dormitories'
import { ElMessage } from 'element-plus'

export const useDormitoryStore = defineStore('dormitory', () => {
  // 状态
  const dormitories = ref([])
  const loading = ref(false)
  const currentDormitory = ref(null)
  const bedStatus = ref(null)

  // 计算属性
  const activeDormitories = computed(() => 
    dormitories.value.filter(dorm => dorm.is_active)
  )

  const totalDormitories = computed(() => dormitories.value.length)

  const totalBeds = computed(() => 
    dormitories.value.reduce((sum, dorm) => sum + dorm.total_beds, 0)
  )

  const occupiedBeds = computed(() => 
    dormitories.value.reduce((sum, dorm) => sum + (dorm.occupied_beds || 0), 0)
  )

  const availableBeds = computed(() => 
    dormitories.value.reduce((sum, dorm) => sum + (dorm.available_beds || dorm.total_beds), 0)
  )

  const occupancyRate = computed(() => {
    const total = totalBeds.value
    const occupied = occupiedBeds.value
    return total > 0 ? ((occupied / total) * 100).toFixed(1) : 0
  })

  // 操作方法
  const fetchDormitories = async (params = {}) => {
    loading.value = true
    try {
      const data = await dormitoryApi.getDormitories(params)
      dormitories.value = data
      return data
    } catch (error) {
      console.error('获取宿舍列表失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const createDormitory = async (dormitoryData) => {
    loading.value = true
    try {
      const newDormitory = await dormitoryApi.createDormitory(dormitoryData)
      dormitories.value.unshift(newDormitory)
      ElMessage.success('宿舍创建成功')
      return newDormitory
    } catch (error) {
      console.error('创建宿舍失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const updateDormitory = async (id, updateData) => {
    loading.value = true
    try {
      const updatedDormitory = await dormitoryApi.updateDormitory(id, updateData)
      const index = dormitories.value.findIndex(dorm => dorm.id === id)
      if (index !== -1) {
        dormitories.value[index] = updatedDormitory
      }
      ElMessage.success('宿舍更新成功')
      return updatedDormitory
    } catch (error) {
      console.error('更新宿舍失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const deleteDormitory = async (id) => {
    loading.value = true
    try {
      await dormitoryApi.deleteDormitory(id)
      dormitories.value = dormitories.value.filter(dorm => dorm.id !== id)
      ElMessage.success('宿舍删除成功')
    } catch (error) {
      console.error('删除宿舍失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const getDormitoryById = async (id) => {
    loading.value = true
    try {
      const dormitory = await dormitoryApi.getDormitory(id)
      currentDormitory.value = dormitory
      return dormitory
    } catch (error) {
      console.error('获取宿舍详情失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const fetchBedStatus = async (id, date = null) => {
    loading.value = true
    try {
      const status = await dormitoryApi.getBedStatus(id, date)
      bedStatus.value = status
      return status
    } catch (error) {
      console.error('获取床位状态失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 重置状态
  const resetState = () => {
    dormitories.value = []
    currentDormitory.value = null
    bedStatus.value = null
    loading.value = false
  }

  return {
    // 状态
    dormitories,
    loading,
    currentDormitory,
    bedStatus,
    
    // 计算属性
    activeDormitories,
    totalDormitories,
    totalBeds,
    occupiedBeds,
    availableBeds,
    occupancyRate,
    
    // 方法
    fetchDormitories,
    createDormitory,
    updateDormitory,
    deleteDormitory,
    getDormitoryById,
    fetchBedStatus,
    resetState
  }
})
