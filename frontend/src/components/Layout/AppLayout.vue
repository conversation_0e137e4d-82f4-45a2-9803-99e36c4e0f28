<template>
  <el-container class="app-layout">
    <!-- 侧边栏 -->
    <el-aside :width="isCollapse ? '64px' : '200px'" class="app-sidebar">
      <div class="sidebar-header">
        <div v-if="!isCollapse" class="logo">
          <h2>宿舍管理系统</h2>
        </div>
        <div v-else class="logo-mini">
          <el-icon size="24"><House /></el-icon>
        </div>
      </div>
      
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapse"
        :unique-opened="true"
        background-color="#304156"
        text-color="#bfcbd9"
        active-text-color="#ffffff"
        router
      >
        <el-menu-item
          v-for="route in menuRoutes"
          :key="route.path"
          :index="route.path"
        >
          <el-icon>
            <component :is="route.meta.icon" />
          </el-icon>
          <template #title>{{ route.meta.title }}</template>
        </el-menu-item>
      </el-menu>
    </el-aside>

    <!-- 主内容区 -->
    <el-container>
      <!-- 顶部导航 -->
      <el-header class="app-header">
        <div class="header-left">
          <el-button
            text
            @click="toggleSidebar"
            class="sidebar-toggle"
          >
            <el-icon size="20">
              <Expand v-if="isCollapse" />
              <Fold v-else />
            </el-icon>
          </el-button>
          
          <el-breadcrumb separator="/">
            <el-breadcrumb-item
              v-for="item in breadcrumbs"
              :key="item.path"
              :to="item.path"
            >
              {{ item.title }}
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        
        <div class="header-right">
          <el-button text @click="refreshPage">
            <el-icon><Refresh /></el-icon>
          </el-button>

          <!-- 用户信息下拉菜单 -->
          <el-dropdown @command="handleUserCommand">
            <div class="user-info">
              <el-icon><User /></el-icon>
              <span class="username">{{ authStore.username || '用户' }}</span>
              <el-icon class="arrow-down"><ArrowDown /></el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="userInfo">
                  <el-icon><User /></el-icon>
                  个人信息
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <el-icon><SwitchButton /></el-icon>
                  退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </el-header>

      <!-- 主内容 -->
      <el-main class="app-main">
        <router-view v-slot="{ Component }">
          <transition name="fade-transform" mode="out-in">
            <component :is="Component" />
          </transition>
        </router-view>
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessageBox, ElMessage } from 'element-plus'
import {
  House,
  DataBoard,
  OfficeBuilding,
  User,
  Document,
  TrendCharts,
  Expand,
  Fold,
  Refresh,
  ArrowDown,
  SwitchButton
} from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// 响应式状态
const isCollapse = ref(false)

// 菜单路由配置
const menuRoutes = [
  {
    path: '/reports',
    meta: { title: '报表统计', icon: 'TrendCharts' }
  },
  {
    path: '/records',
    meta: { title: '入住记录', icon: 'Document' }
  },
  {
    path: '/departments',
    meta: { title: '部门管理', icon: 'OfficeBuilding' }
  },
  {
    path: '/dormitories',
    meta: { title: '宿舍管理', icon: 'House' }
  },
  {
    path: '/residents',
    meta: { title: '住户管理', icon: 'User' }
  }
]

// 计算属性
const activeMenu = computed(() => route.path)

const breadcrumbs = computed(() => {
  const matched = route.matched.filter(item => item.meta && item.meta.title)
  return matched.map(item => ({
    path: item.path,
    title: item.meta.title
  }))
})

// 方法
const toggleSidebar = () => {
  isCollapse.value = !isCollapse.value
}

// 主题切换功能已移除

const refreshPage = () => {
  router.go(0)
}

// 处理用户下拉菜单命令
const handleUserCommand = async (command) => {
  switch (command) {
    case 'userInfo':
      ElMessage.info('个人信息功能开发中...')
      break
    case 'logout':
      try {
        await ElMessageBox.confirm(
          '确定要退出登录吗？',
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )

        await authStore.logout()
        router.push('/login')
      } catch (error) {
        // 用户取消操作
        if (error !== 'cancel') {
          console.error('退出登录失败:', error)
        }
      }
      break
  }
}

// 监听路由变化
watch(
  () => route.path,
  () => {
    // 可以在这里添加路由变化的逻辑
  }
)
</script>

<style lang="scss" scoped>
.app-layout {
  height: 100vh;
}

.app-sidebar {
  background-color: #304156;
  transition: width 0.3s;
  
  .sidebar-header {
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 1px solid #434a50;
    
    .logo {
      h2 {
        color: #ffffff;
        margin: 0;
        font-size: 16px;
        font-weight: 600;
      }
    }
    
    .logo-mini {
      color: #ffffff;
    }
  }
  
  .el-menu {
    border-right: none;
  }
}

.app-header {
  background-color: #ffffff;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  
  .header-left {
    display: flex;
    align-items: center;
    gap: 20px;
    
    .sidebar-toggle {
      color: #606266;
      
      &:hover {
        color: #409eff;
      }
    }
  }
  
  .header-right {
    display: flex;
    align-items: center;
    gap: 15px;

    .el-button {
      color: #606266;

      &:hover {
        color: #409eff;
      }
    }

    .user-info {
      display: flex;
      align-items: center;
      gap: 6px;
      padding: 8px 12px;
      border-radius: 6px;
      cursor: pointer;
      color: #606266;
      transition: all 0.3s;

      &:hover {
        background-color: #f5f7fa;
        color: #409eff;
      }

      .username {
        font-size: 14px;
        font-weight: 500;
      }

      .arrow-down {
        font-size: 12px;
        transition: transform 0.3s;
      }
    }
  }
}

.app-main {
  background-color: #f5f7fa;
  padding: 20px;
  overflow-y: auto;
}

// 页面切换动画
.fade-transform-enter-active,
.fade-transform-leave-active {
  transition: all 0.3s;
}

.fade-transform-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}
</style>
