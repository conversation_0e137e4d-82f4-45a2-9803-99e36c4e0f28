// 全局样式文件

// 导入NProgress样式
@import 'nprogress/nprogress.css';

// 自定义CSS变量
:root {
  --app-primary-color: #409eff;
  --app-success-color: #67c23a;
  --app-warning-color: #e6a23c;
  --app-danger-color: #f56c6c;
  --app-info-color: #909399;
  
  --app-bg-color: #f5f7fa;
  --app-sidebar-bg: #304156;
  --app-sidebar-text: #bfcbd9;
  --app-sidebar-active-text: #ffffff;
  
  --app-border-radius: 4px;
  --app-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  --app-transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
}

// 重置样式
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', 'PingFang SC', 
               'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', 
               Helvetica, Arial, sans-serif;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

// NProgress自定义样式
#nprogress .bar {
  background: var(--app-primary-color) !important;
  height: 3px !important;
}

#nprogress .peg {
  box-shadow: 0 0 10px var(--app-primary-color), 0 0 5px var(--app-primary-color) !important;
}

// 布局相关样式
.app-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--el-border-color-light);
  
  .page-title {
    font-size: 24px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    margin: 0;
  }
  
  .page-actions {
    display: flex;
    gap: 12px;
  }
}

// 卡片样式
.app-card {
  background: #ffffff;
  border-radius: var(--app-border-radius);
  box-shadow: var(--app-box-shadow);
  padding: 20px;
  margin-bottom: 20px;
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    .card-title {
      font-size: 16px;
      font-weight: 600;
      color: var(--el-text-color-primary);
      margin: 0;
    }
  }
}

// 统计卡片样式
.stats-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  border-radius: var(--app-border-radius);
  padding: 24px;
  text-align: center;
  box-shadow: var(--app-box-shadow);
  transition: var(--app-transition);
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
  }
  
  .stats-icon {
    font-size: 32px;
    margin-bottom: 12px;
    opacity: 0.9;
  }
  
  .stats-value {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 8px;
  }
  
  .stats-label {
    font-size: 14px;
    opacity: 0.9;
  }
}

// 表格样式增强
.el-table {
  .el-table__header {
    th {
      background-color: #fafafa;
      color: var(--el-text-color-primary);
      font-weight: 600;
    }
  }
  
  .el-table__row {
    &:hover {
      background-color: #f5f7fa;
    }
  }
}

// 表单样式增强
.el-form {
  .el-form-item__label {
    font-weight: 500;
  }
}

// 按钮样式增强
.el-button {
  border-radius: var(--app-border-radius);
  transition: var(--app-transition);
  
  &.is-plain {
    &:hover {
      transform: translateY(-1px);
    }
  }
}

// 对话框样式增强
.el-dialog {
  border-radius: 8px;
  
  .el-dialog__header {
    padding: 20px 20px 10px;
    border-bottom: 1px solid var(--el-border-color-lighter);
    
    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
    }
  }
  
  .el-dialog__body {
    padding: 20px;
  }
  
  .el-dialog__footer {
    padding: 10px 20px 20px;
    border-top: 1px solid var(--el-border-color-lighter);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .app-container {
    padding: 10px;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    
    .page-actions {
      width: 100%;
      justify-content: flex-end;
    }
  }
  
  .stats-card {
    margin-bottom: 16px;
  }
}

// 工具类
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 8px; }
.mb-2 { margin-bottom: 16px; }
.mb-3 { margin-bottom: 24px; }
.mb-4 { margin-bottom: 32px; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 8px; }
.mt-2 { margin-top: 16px; }
.mt-3 { margin-top: 24px; }
.mt-4 { margin-top: 32px; }

.flex { display: flex; }
.flex-1 { flex: 1; }
.justify-between { justify-content: space-between; }
.justify-center { justify-content: center; }
.items-center { align-items: center; }

.w-full { width: 100%; }
.h-full { height: 100%; }
