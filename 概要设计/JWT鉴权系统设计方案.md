# JWT鉴权系统设计方案

## 1. 项目概述

### 1.1 需求背景
基于现有的宿舍入住管理系统，需要实现一个完整的用户认证和授权系统：
- 使用LDAP进行用户身份验证
- 使用JWT Token进行API访问控制
- 保护除登录接口外的所有API端点

### 1.2 技术栈
- **后端框架**: FastAPI
- **认证方式**: LDAP + JWT
- **数据库**: SQLite (现有)
- **LDAP库**: ldap3
- **JWT库**: python-jose[cryptography]

## 2. 系统架构设计

### 2.1 整体架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用      │    │   FastAPI后端   │    │   LDAP服务器    │
│                 │    │                 │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │  登录页面   │ │───▶│ │  登录接口   │ │───▶│ │  用户验证   │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
│                 │    │        │        │    │                 │
│ ┌─────────────┐ │    │        ▼        │    └─────────────────┘
│ │  业务页面   │ │◀───│ ┌─────────────┐ │
│ └─────────────┘ │    │ │ JWT Token   │ │
│                 │    │ │   生成      │ │
└─────────────────┘    │ └─────────────┘ │
                       │        │        │
                       │        ▼        │
                       │ ┌─────────────┐ │
                       │ │ 受保护的API │ │
                       │ │   端点      │ │
                       │ └─────────────┘ │
                       └─────────────────┘
```

### 2.2 认证流程
1. **用户登录**: 前端发送用户名/密码到登录接口
2. **LDAP验证**: 后端通过LDAP服务器验证用户身份
3. **Token生成**: 验证成功后生成JWT Token返回给前端
4. **Token存储**: 前端存储Token（localStorage/sessionStorage）
5. **API访问**: 后续请求携带Token访问受保护的API
6. **Token验证**: 后端验证Token有效性并处理请求

## 3. 详细技术设计

### 3.1 目录结构设计
```
app/
├── auth/                    # 认证模块
│   ├── __init__.py
│   ├── ldap_auth.py        # LDAP认证逻辑
│   ├── jwt_handler.py      # JWT处理逻辑
│   └── dependencies.py     # 认证依赖注入
├── api/v1/
│   ├── auth.py            # 认证API路由
│   └── ...                # 现有API路由
├── schemas/
│   ├── auth.py            # 认证数据模型
│   └── ...                # 现有数据模型
├── core/
│   ├── config.py          # 配置文件（需更新）
│   └── ...                # 现有核心模块
└── models/
    ├── user.py            # 用户模型（可选）
    └── ...                # 现有数据模型
```

### 3.2 配置更新
在 `app/core/config.py` 中添加LDAP和JWT配置：

```python
class Settings(BaseSettings):
    # ... 现有配置 ...
    
    # LDAP配置
    ldap_uri: str = Field(default="ldap://**************:389", description="LDAP服务器地址")
    ldap_base_dn_users: str = Field(
        default="dc=users,dc=appdata,dc=erayt,dc=com", 
        description="LDAP用户基础DN"
    )
    ldap_connect_timeout: int = Field(default=3, description="LDAP连接超时时间(秒)")
    
    # JWT配置（已存在，可能需要调整）
    secret_key: str = Field(
        default="your-secret-key-change-in-production",
        description="JWT密钥"
    )
    algorithm: str = Field(default="HS256", description="JWT算法")
    access_token_expire_minutes: int = Field(default=30, description="访问令牌过期时间(分钟)")
```

### 3.3 LDAP认证模块
创建 `app/auth/ldap_auth.py`：

```python
from ldap3 import Server, Connection, ALL, core
from app.core.config import settings
from app.core.logging import get_logger

logger = get_logger(__name__)

def get_user_dn(username: str) -> str:
    """构建用户DN"""
    return f"cn={username},{settings.ldap_base_dn_users}"

def authenticate_user(username: str, password: str) -> bool:
    """LDAP用户认证"""
    user_dn = get_user_dn(username)
    server = Server(settings.ldap_uri, get_info=ALL, connect_timeout=settings.ldap_connect_timeout)
    
    try:
        conn = Connection(
            server,
            user=user_dn,
            password=password,
            authentication='SIMPLE',
            auto_bind=True,
        )
        conn.unbind()
        logger.info(f"LDAP用户认证成功: {username}")
        return True
    except core.exceptions.LDAPBindError as e:
        logger.error(f"LDAP用户认证失败: {username}, {e}")
        return False
    except Exception as e:
        logger.error(f"LDAP服务连接失败: {username}, {e}")
        return False
```

### 3.4 JWT处理模块
创建 `app/auth/jwt_handler.py`：

```python
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from jose import JWTError, jwt
from app.core.config import settings
from app.core.logging import get_logger

logger = get_logger(__name__)

def create_access_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
    """创建JWT访问令牌"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=settings.access_token_expire_minutes)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.secret_key, algorithm=settings.algorithm)
    return encoded_jwt

def verify_token(token: str) -> Optional[Dict[str, Any]]:
    """验证JWT令牌"""
    try:
        payload = jwt.decode(token, settings.secret_key, algorithms=[settings.algorithm])
        return payload
    except JWTError as e:
        logger.error(f"JWT令牌验证失败: {e}")
        return None
```

### 3.5 认证依赖注入
创建 `app/auth/dependencies.py`：

```python
from typing import Optional
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from app.auth.jwt_handler import verify_token

security = HTTPBearer()

def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> str:
    """获取当前用户信息"""
    token = credentials.credentials
    payload = verify_token(token)
    
    if payload is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的认证令牌",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    username: str = payload.get("sub")
    if username is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的认证令牌",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return username
```

## 4. API接口设计

### 4.1 认证相关接口
创建 `app/api/v1/auth.py`：

#### 4.1.1 登录接口
```
POST /api/v1/auth/login
Content-Type: application/json

请求体:
{
    "username": "string",
    "password": "string"
}

响应:
{
    "access_token": "string",
    "token_type": "bearer",
    "expires_in": 1800,
    "user_info": {
        "username": "string"
    }
}
```

#### 4.1.2 用户信息接口
```
GET /api/v1/auth/me
Authorization: Bearer <token>

响应:
{
    "username": "string",
    "login_time": "2024-01-01T12:00:00Z"
}
```

### 4.2 受保护接口
所有现有的业务API接口都需要添加认证依赖：
- `/api/v1/departments/*`
- `/api/v1/dormitories/*`
- `/api/v1/residents/*`
- `/api/v1/records/*`
- `/api/v1/reports/*`

## 5. 数据模型设计

### 5.1 认证相关模型
创建 `app/schemas/auth.py`：

```python
from pydantic import BaseModel, Field
from typing import Optional

class LoginRequest(BaseModel):
    """登录请求模型"""
    username: str = Field(..., description="用户名")
    password: str = Field(..., description="密码")

class LoginResponse(BaseModel):
    """登录响应模型"""
    access_token: str = Field(..., description="访问令牌")
    token_type: str = Field(default="bearer", description="令牌类型")
    expires_in: int = Field(..., description="过期时间(秒)")
    user_info: "UserInfo" = Field(..., description="用户信息")

class UserInfo(BaseModel):
    """用户信息模型"""
    username: str = Field(..., description="用户名")

class TokenPayload(BaseModel):
    """JWT载荷模型"""
    sub: str = Field(..., description="用户标识")
    exp: int = Field(..., description="过期时间戳")
```

## 6. 安全考虑

### 6.1 Token安全
- **密钥管理**: 使用强随机密钥，生产环境通过环境变量配置
- **过期时间**: 设置合理的过期时间（默认30分钟）
- **算法选择**: 使用HS256算法进行签名

### 6.2 LDAP安全
- **连接安全**: 配置连接超时，防止长时间阻塞
- **错误处理**: 详细记录认证失败日志，便于问题排查
- **重试机制**: 实现连接重试，提高系统稳定性

### 6.3 API安全
- **HTTPS**: 生产环境必须使用HTTPS
- **CORS**: 配置合适的跨域策略
- **请求限制**: 实现登录接口的频率限制

## 7. 实施计划

### 7.1 第一阶段：基础设施
- [ ] 更新项目依赖（ldap3等）
- [ ] 更新配置文件
- [ ] 创建认证模块目录结构

### 7.2 第二阶段：核心功能
- [ ] 实现LDAP认证模块
- [ ] 实现JWT处理模块
- [ ] 创建认证依赖注入

### 7.3 第三阶段：API集成
- [ ] 创建登录API接口
- [ ] 更新现有API路由，添加认证依赖
- [ ] 在main.py中注册认证路由

### 7.4 第四阶段：测试验证
- [ ] 单元测试
- [ ] 集成测试
- [ ] 安全测试

## 8. 测试策略

### 8.1 单元测试
- LDAP认证功能测试
- JWT生成和验证测试
- 认证依赖注入测试

### 8.2 集成测试
- 登录流程端到端测试
- API访问权限测试
- Token过期处理测试

### 8.3 安全测试
- 无效Token访问测试
- 过期Token处理测试
- 恶意请求防护测试


## 10. 维护和监控

### 10.1 日志监控
- 认证成功/失败日志
- Token生成和验证日志
- LDAP连接状态日志

