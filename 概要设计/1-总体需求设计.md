
## 🏠 宿舍入住管理系统 - 需求文档

### 一、系统目标

开发一个宿舍入住情况管理系统，能够：

- 记录人员入住与离开信息
    
- 支持按月统计数据（床位使用明细、部门分摊比例等）
    
- 支持维护宿舍床位信息
    
- 提供合理的部门费用分摊依据
    

---

### 二、主要功能需求

#### 1. 入住信息录入

用户可以录入以下信息：

- 姓名
    
- 所属部门
    
- 入住时间（年月日）：用户入住的时候登记
    
- 离开时间（年月日）：用户离开的时候登记
    

#### 2. 月度统计

系统按月统计并输出：

- 每月床位使用明细（入住人员、所属部门、入住时长（按天维度）等）
    
- 各部门的费用分摊比例（详见分摊规则）
    

#### 3. 宿舍管理

- 系统支持管理多个宿舍（目前为 8 个）
    
- 每个宿舍床位数量不固定，需支持配置
    
- 支持宿舍维护与变更操作
    

#### 4. 费用分摊逻辑

- 分摊单位为床位天数（床位 × 天）
    
- 某天某宿舍如果入住了多个部门的成员，则当日费用按床位部门平均分摊  
    例如：5 个床位分别属于 5 个部门 → 每部门承担 20%
    
- 如果仅一个部门有人员入住（其余空床），则该部门承担全部费用（100%）
    
- 如果没有人员入住，则默认公司承担全部费用（100%）
---

### 三、其他说明

- 系统需支持跨月计算

- 系统需支持每天可查看报告，每天的报告统计周期为当月第一天到当前时间
	
- 离开时间可以为空，表示当前仍在住宿
    
- 需能导出月度统计报表（如 Excel 或 PDF）
    

---


---

## ✅ 分摊统计算法设计

### 🎯 核心目标

基于：

- 人员入住记录（包含入住时间、离开时间、所属部门）
    
- 床位信息（每个宿舍的床位数量）  
    输出：
    
- 每天每个宿舍的 **床位×部门** 分布
    
- 每月各部门的 **床位天数**
    
- 各部门月度费用分摊比例（用于结算）
    

---

## 📊 数据结构设计

### 1. 人员入住信息（`residents`）

```python
{
  "name": "张三",
  "department": "技术部",
  "checkin_date": "2025-07-01",
  "checkout_date": "2025-07-15"  # 若为空，表示至今仍在住
  "dormitory_id": "D1"
}
```

### 2. 宿舍床位配置（`dorms`）

```python
{
  "dormitory_id": "D1",
  "beds": 5
}
```

---

## 🧮 计算逻辑

### Step 1️⃣：构建每天宿舍入住映射

对目标月份的每一天，统计每个宿舍中，每个部门所占用的床位数量。

例如：

```python
{
  "2025-07-02": {
    "D1": {
      "技术部": 2,
      "财务部": 1
    },
    "D2": {
      "市场部": 1
    }
  }
}
```

### Step 2️⃣：计算每天每个部门的分摊比例

假设宿舍床位为 5 个，入住如下：

- 技术部：2人
    
- 财务部：1人
    
- 其他2个床位空置
    

计算逻辑如下：

```python
total_beds_used = 3
departments = {"技术部": 2, "财务部": 1}

for dept in departments:
    dept_share = departments[dept] / total_beds_used
```

⚠️ 如果当日没有人入住，则该宿舍费用全部归公司承担。

---

### Step 3️⃣：累计每个部门的床位天数

遍历每天的分摊记录，把每天各部门在各宿舍的比例加总成该部门本月的总床位天数。

```python
department_total = defaultdict(float)

for each_day in month_days:
    for dorm in day_data[each_day]:
        for dept, share in dorm.items():
            department_total[dept] += share  # 按照比例统计
```

---

### Step 4️⃣：计算各部门费用占比

假设公司设定的每床每日费用为 `unit_cost`，则：

```python
dept_cost = {
    dept: bed_days * unit_cost
    for dept, bed_days in department_total.items()
}
```

---

## 📘 示例输出结构

```python
{
  "2025-07": {
    "技术部": {
      "bed_days": 38.2,
      "cost": 3820,
      "ratio": "38.2%"
    },
    "财务部": {
      "bed_days": 15.8,
      "cost": 1580,
      "ratio": "15.8%"
    },
    ...
  }
}
```

---

## 📤 导出支持

最终支持导出：

- 按天的明细（宿舍、床位、部门）
    
- 按月的汇总（部门、床位天数、费用）
    

---

## 🧠 边界与注意事项

1. 离开时间为空时，默认统计到当前日期
    
2. 一个人最多占一个床位
    
3. 同一天同一个人不会重复入住宿舍
    
4. 支持多宿舍、跨月统计
    

---

如你有数据库结构或技术栈（例如 Python + SQLAlchemy / Pandas 等），我也可以给出具体实现。需要吗？