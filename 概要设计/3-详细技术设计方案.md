# 宿舍入住管理系统 - 详细技术设计方案

## 1. 项目概述

### 1.1 需求背景

随着企业规模扩大，宿舍管理成为人力资源管理的重要组成部分。现有的宿舍管理方式存在以下问题：

- **数据记录不规范**：入住离开信息记录分散，难以统一管理
- **费用分摊不透明**：各部门费用分摊缺乏科学依据和计算标准
- **统计效率低下**：月度统计需要人工汇总，耗时且容易出错
- **报表生成困难**：缺乏标准化的报表输出格式

因此，需要开发一套宿舍入住管理系统，实现入住信息的标准化管理和费用分摊的自动化计算。

### 1.2 项目目标

构建一个功能完整、操作便捷的宿舍入住管理Web系统，具备以下核心能力：

- **入住管理**：支持人员入住和离开信息的录入与维护
- **费用分摊**：基于科学算法自动计算各部门费用分摊比例
- **统计报表**：提供多维度的统计分析和报表导出功能
- **宿舍配置**：支持宿舍信息和床位配置的灵活管理

### 1.3 技术架构选型

- **后端框架**：FastAPI + SQLAlchemy + Pydantic
- **前端框架**：Vue 3 + Composition API + Tailwind CSS
- **数据库**：SQLite (开发) / PostgreSQL (生产)
- **部署架构**：前后端不分离，统一部署

## 2. 业务架构设计

### 2.1 领域模型设计

```mermaid
classDiagram
    class Department {
        +String id
        +String name
        +String description
        +bool is_active
        +DateTime created_at
        +DateTime updated_at
    }

    class Dormitory {
        +String id
        +String name
        +int total_beds
        +String description
        +bool is_active
        +DateTime created_at
        +DateTime updated_at
    }

    class Resident {
        +String id
        +String name
        +String employee_id
        +String phone
        +String email
        +String department_id
        +bool is_active
        +DateTime created_at
        +DateTime updated_at
    }

    class ResidenceRecord {
        +String id
        +String resident_id
        +String dormitory_id
        +int bed_number
        +Date check_in_date
        +Date check_out_date
        +String status
        +String notes
        +DateTime created_at
        +DateTime updated_at
    }

    class MonthlyReport {
        +String id
        +int year
        +int month
        +Date report_date
        +float total_bed_days
        +Map department_summary  %% map of department_id -> bed_days
        +List daily_details      %% list of DailyAllocation
        +DateTime created_at
    }

    class DailyAllocation {
        +String id
        +Date allocation_date
        +String dormitory_id
        +int total_beds
        +int occupied_beds
        +Map department_allocations  %% map of department_id -> bed_count
        +String monthly_report_id
        +DateTime created_at
    }

    Department o-- Resident : belongs to
    Resident o-- ResidenceRecord : has many
    Dormitory o-- ResidenceRecord : has many
    MonthlyReport o-- DailyAllocation : contains
    Dormitory o-- DailyAllocation : has many

```

### 2.2 核心业务流程

#### 2.2.1 入住流程

```mermaid
flowchart TD
	B[管理员录入基本信息]
    B --> C[选择宿舍和床位]
    C --> D[设置入住日期]
    D --> E[保存入住记录]
    E --> F[自动更新宿舍状态]
    F --> H[入住完成]
    
    C --> I{床位是否可用?}
    I -->|否| J[提示床位冲突]
    J --> C
    I -->|是| D
```

#### 2.2.2 离开流程

```mermaid
flowchart TD
     B[管理员录入离开信息]
    B --> C[设置离开日期]
    C --> D[更新入住记录]
    D --> E[释放床位资源]
    E --> F[更新宿舍状态]
    F --> G[重新计算分摊费用]
    G --> H[离开完成]
    
    C --> I{离开日期是否合理?}
    I -->|否| J[提示日期错误]
    J --> C
    I -->|是| D
```

#### 2.2.3 费用分摊计算流程

```mermaid
sequenceDiagram
    participant UI as 前端界面
    participant API as FastAPI接口
    participant Service as 分摊服务
    participant DB as 数据库
    
    UI->>API: 请求月度报告
    API->>Service: 调用计算服务
    Service->>DB: 查询入住记录
    DB-->>Service: 返回记录数据
    Service->>Service: 执行分摊算法
    loop 每日分摊计算
        Service->>Service: 计算当日各宿舍分摊
        Service->>Service: 汇总部门床位天数
    end
    Service->>DB: 保存计算结果
    Service-->>API: 返回报告数据
    API-->>UI: 返回JSON响应
    UI->>UI: 渲染报表界面
```

## 3. 系统架构设计

### 3.1 整体架构图

```mermaid
graph TB
    subgraph "前端层"
        A[Vue 3 + Tailwind CSS]
        B[状态管理 Pinia]
        C[路由管理 Vue Router]
        D[HTTP客户端 Axios]
    end
    
    subgraph "后端层"
        E[FastAPI 应用]
        F[路由层 APIRouter]
        G[服务层 Services]
        H[数据访问层 Repository]
    end
    
    subgraph "数据层"
        I[SQLAlchemy ORM]
        J[数据库 SQLite/PostgreSQL]
    end
    
    subgraph "外部服务"
        K[文件导出服务]
        L[日志服务]
    end
    
    A --> D
    D --> E
    E --> F
    F --> G
    G --> H
    H --> I
    I --> J
    G --> K
    G --> L
```

### 3.2 项目目录结构

```
BedSharingCalc/
├── app/
│   ├── api/                     # API路由层
│   │   ├── __init__.py
│   │   ├── v1/
│   │   │   ├── __init__.py
│   │   │   ├── departments.py   # 部门管理API
│   │   │   ├── dormitories.py   # 宿舍管理API
│   │   │   ├── residents.py     # 住户管理API
│   │   │   ├── records.py       # 入住记录API
│   │   │   └── reports.py       # 报表统计API
│   │   └── deps.py              # 依赖注入
│   ├── core/                    # 核心配置
│   │   ├── __init__.py
│   │   ├── config.py            # 应用配置
│   │   ├── database.py          # 数据库配置
│   │   ├── logging.py           # 日志配置
│   │   └── security.py          # 安全配置
│   ├── models/                  # 数据模型
│   │   ├── __init__.py
│   │   ├── base.py              # 基础模型
│   │   ├── department.py        # 部门模型
│   │   ├── dormitory.py         # 宿舍模型
│   │   ├── resident.py          # 住户模型
│   │   ├── record.py            # 入住记录模型
│   │   └── report.py            # 报表模型
│   ├── schemas/                 # Pydantic模式
│   │   ├── __init__.py
│   │   ├── department.py
│   │   ├── dormitory.py
│   │   ├── resident.py
│   │   ├── record.py
│   │   └── report.py
│   ├── services/                # 业务服务层
│   │   ├── __init__.py
│   │   ├── allocation_calculator.py  # 分摊计算服务
│   │   ├── department_service.py     # 部门服务
│   │   ├── dormitory_service.py      # 宿舍服务
│   │   ├── resident_service.py       # 住户服务
│   │   ├── record_service.py         # 记录服务
│   │   └── report_service.py         # 报表服务
│   ├── repositories/            # 数据访问层
│   │   ├── __init__.py
│   │   ├── base.py              # 基础仓储
│   │   ├── department_repo.py
│   │   ├── dormitory_repo.py
│   │   ├── resident_repo.py
│   │   ├── record_repo.py
│   │   └── report_repo.py
│   ├── utils/                   # 工具类
│   │   ├── __init__.py
│   │   ├── date_utils.py        # 日期工具
│   │   ├── export_utils.py      # 导出工具
│   │   └── validation_utils.py  # 验证工具
│   └── main.py                  # 应用入口
├── frontend/                    # 前端代码
│   ├── components/              # Vue组件
│   │   ├── common/              # 通用组件
│   │   ├── department/          # 部门相关组件
│   │   ├── dormitory/           # 宿舍相关组件
│   │   ├── resident/            # 住户相关组件
│   │   ├── record/              # 记录相关组件
│   │   └── report/              # 报表相关组件
│   ├── pages/                   # 页面组件
│   ├── composables/             # 组合式函数
│   ├── stores/                  # Pinia状态管理
│   ├── utils/                   # 前端工具
│   ├── assets/                  # 静态资源
│   └── main.js                  # 前端入口
├── tests/                       # 测试代码
├── migrations/                  # 数据库迁移
├── docs/                        # 文档
├── requirements.txt             # Python依赖
├── package.json                 # Node.js依赖
└── README.md                    # 项目说明
```

## 4. 数据模型设计

### 4.1 数据库表结构设计

#### 4.1.1 部门表 (departments)

```sql
CREATE TABLE departments (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_departments_name ON departments(name);
CREATE INDEX idx_departments_active ON departments(is_active);
```

#### 4.1.2 宿舍表 (dormitories)

```sql
CREATE TABLE dormitories (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    total_beds INTEGER NOT NULL CHECK (total_beds > 0),
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_dormitories_name ON dormitories(name);
CREATE INDEX idx_dormitories_active ON dormitories(is_active);
```

#### 4.1.3 住户表 (residents)

```sql
CREATE TABLE residents (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    employee_id VARCHAR(50) UNIQUE,
    phone VARCHAR(20),
    email VARCHAR(100),
    department_id VARCHAR(50) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (department_id) REFERENCES departments(id)
);

-- 创建索引
CREATE INDEX idx_residents_name ON residents(name);
CREATE INDEX idx_residents_employee_id ON residents(employee_id);
CREATE INDEX idx_residents_department ON residents(department_id);
CREATE INDEX idx_residents_active ON residents(is_active);
```

#### 4.1.4 入住记录表 (residence_records)

```sql
CREATE TABLE residence_records (
    id VARCHAR(50) PRIMARY KEY,
    resident_id VARCHAR(50) NOT NULL,
    dormitory_id VARCHAR(50) NOT NULL,
    bed_number INTEGER NOT NULL,
    check_in_date DATE NOT NULL,
    check_out_date DATE,
    status VARCHAR(20) DEFAULT 'ACTIVE' CHECK (status IN ('ACTIVE', 'COMPLETED', 'CANCELLED')),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (resident_id) REFERENCES residents(id),
    FOREIGN KEY (dormitory_id) REFERENCES dormitories(id),
    CONSTRAINT chk_checkout_after_checkin CHECK (check_out_date IS NULL OR check_out_date >= check_in_date),
    CONSTRAINT chk_bed_number_positive CHECK (bed_number > 0)
);

-- 创建索引
CREATE INDEX idx_records_resident ON residence_records(resident_id);
CREATE INDEX idx_records_dormitory ON residence_records(dormitory_id);
CREATE INDEX idx_records_dates ON residence_records(check_in_date, check_out_date);
CREATE INDEX idx_records_status ON residence_records(status);
CREATE UNIQUE INDEX idx_records_bed_conflict ON residence_records(dormitory_id, bed_number, check_in_date, check_out_date)
    WHERE status = 'ACTIVE';
```

#### 4.1.5 月度报告表 (monthly_reports)

```sql
CREATE TABLE monthly_reports (
    id VARCHAR(50) PRIMARY KEY,
    year INTEGER NOT NULL,
    month INTEGER NOT NULL CHECK (month BETWEEN 1 AND 12),
    report_date DATE NOT NULL,
    total_bed_days DECIMAL(10,2) NOT NULL,
    department_summary JSON NOT NULL,
    daily_details JSON NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT uk_monthly_reports_year_month UNIQUE (year, month)
);

-- 创建索引
CREATE INDEX idx_reports_year_month ON monthly_reports(year, month);
CREATE INDEX idx_reports_date ON monthly_reports(report_date);
```

#### 4.1.6 日度分摊表 (daily_allocations)

```sql
CREATE TABLE daily_allocations (
    id VARCHAR(50) PRIMARY KEY,
    allocation_date DATE NOT NULL,
    dormitory_id VARCHAR(50) NOT NULL,
    total_beds INTEGER NOT NULL,
    occupied_beds INTEGER NOT NULL,
    department_allocations JSON NOT NULL,
    monthly_report_id VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (dormitory_id) REFERENCES dormitories(id),
    FOREIGN KEY (monthly_report_id) REFERENCES monthly_reports(id),
    CONSTRAINT chk_occupied_beds_valid CHECK (occupied_beds >= 0 AND occupied_beds <= total_beds)
);

-- 创建索引
CREATE INDEX idx_allocations_date ON daily_allocations(allocation_date);
CREATE INDEX idx_allocations_dormitory ON daily_allocations(dormitory_id);
CREATE INDEX idx_allocations_report ON daily_allocations(monthly_report_id);
CREATE UNIQUE INDEX idx_allocations_unique ON daily_allocations(allocation_date, dormitory_id);
```

### 4.2 SQLAlchemy模型定义

#### 4.2.1 基础模型类

```python
# app/models/base.py
from sqlalchemy import Column, String, DateTime, Boolean
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
import uuid

Base = declarative_base()

class BaseModel(Base):
    """基础模型类"""
    __abstract__ = True
    
    id = Column(String(50), primary_key=True, default=lambda: str(uuid.uuid4()))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    def to_dict(self):
        """转换为字典"""
        return {column.name: getattr(self, column.name) for column in self.__table__.columns}
```

#### 4.2.2 部门模型

```python
# app/models/department.py
from sqlalchemy import Column, String, Text, Boolean
from sqlalchemy.orm import relationship
from .base import BaseModel

class Department(BaseModel):
    """部门模型"""
    __tablename__ = "departments"
    
    name = Column(String(100), nullable=False, unique=True, comment="部门名称")
    description = Column(Text, comment="部门描述")
    is_active = Column(Boolean, default=True, comment="是否启用")
    
    # 关联关系
    residents = relationship("Resident", back_populates="department", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Department(id={self.id}, name={self.name})>"
```

#### 4.2.3 宿舍模型

```python
# app/models/dormitory.py
from sqlalchemy import Column, String, Integer, Text, Boolean, CheckConstraint
from sqlalchemy.orm import relationship
from .base import BaseModel

class Dormitory(BaseModel):
    """宿舍模型"""
    __tablename__ = "dormitories"
    
    name = Column(String(100), nullable=False, unique=True, comment="宿舍名称")
    total_beds = Column(Integer, nullable=False, comment="床位总数")
    description = Column(Text, comment="宿舍描述")
    is_active = Column(Boolean, default=True, comment="是否启用")
    
    # 约束
    __table_args__ = (
        CheckConstraint('total_beds > 0', name='chk_total_beds_positive'),
    )
    
    # 关联关系
    residence_records = relationship("ResidenceRecord", back_populates="dormitory")
    daily_allocations = relationship("DailyAllocation", back_populates="dormitory")
    
    def __repr__(self):
        return f"<Dormitory(id={self.id}, name={self.name}, beds={self.total_beds})>"
```

#### 4.2.4 住户模型

```python
# app/models/resident.py
from sqlalchemy import Column, String, ForeignKey
from sqlalchemy.orm import relationship
from .base import BaseModel

class Resident(BaseModel):
    """住户模型"""
    __tablename__ = "residents"
    
    name = Column(String(100), nullable=False, comment="姓名")
    employee_id = Column(String(50), unique=True, comment="员工号")
    phone = Column(String(20), comment="电话号码")
    email = Column(String(100), comment="邮箱地址")
    department_id = Column(String(50), ForeignKey("departments.id"), nullable=False, comment="部门ID")
    is_active = Column(Boolean, default=True, comment="是否启用")
    
    # 关联关系
    department = relationship("Department", back_populates="residents")
    residence_records = relationship("ResidenceRecord", back_populates="resident")
    
    def __repr__(self):
        return f"<Resident(id={self.id}, name={self.name}, employee_id={self.employee_id})>"
```

#### 4.2.5 入住记录模型

```python
# app/models/record.py
from sqlalchemy import Column, String, Integer, Date, Text, ForeignKey, CheckConstraint
from sqlalchemy.orm import relationship
from .base import BaseModel

class ResidenceRecord(BaseModel):
    """入住记录模型"""
    __tablename__ = "residence_records"
    
    resident_id = Column(String(50), ForeignKey("residents.id"), nullable=False, comment="住户ID")
    dormitory_id = Column(String(50), ForeignKey("dormitories.id"), nullable=False, comment="宿舍ID")
    bed_number = Column(Integer, nullable=False, comment="床位号")
    check_in_date = Column(Date, nullable=False, comment="入住日期")
    check_out_date = Column(Date, comment="离开日期")
    status = Column(String(20), default="ACTIVE", comment="状态")
    notes = Column(Text, comment="备注")
    
    # 约束
    __table_args__ = (
        CheckConstraint('check_out_date IS NULL OR check_out_date >= check_in_date', 
                       name='chk_checkout_after_checkin'),
        CheckConstraint('bed_number > 0', name='chk_bed_number_positive'),
        CheckConstraint("status IN ('ACTIVE', 'COMPLETED', 'CANCELLED')", name='chk_status_valid'),
    )
    
    # 关联关系
    resident = relationship("Resident", back_populates="residence_records")
    dormitory = relationship("Dormitory", back_populates="residence_records")
    
    def __repr__(self):
        return f"<ResidenceRecord(id={self.id}, resident={self.resident_id}, dormitory={self.dormitory_id})>"
```

## 5. API接口设计

### 5.1 API架构设计

```mermaid
graph TD
    A[Client Request] --> B[FastAPI Router]
    B --> C[Request Validation]
    C --> D[Service Layer]
    D --> E[Repository Layer]
    E --> F[Database]
    F --> E
    E --> D
    D --> G[Response Schema]
    G --> H[JSON Response]
    H --> A
    
    subgraph "中间件层"
        I[CORS中间件]
        J[日志中间件]
        K[异常处理中间件]
    end
    
    B --> I
    I --> J
    J --> K
    K --> C
```

### 5.2 核心API接口定义

#### 5.2.1 部门管理API

```python
# app/api/v1/departments.py
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.api.deps import get_db
from app.schemas.department import DepartmentCreate, DepartmentUpdate, DepartmentResponse
from app.services.department_service import DepartmentService

router = APIRouter()

@router.get("/", response_model=List[DepartmentResponse], summary="获取部门列表")
async def get_departments(
    skip: int = 0,
    limit: int = 100,
    is_active: bool = None,
    db: Session = Depends(get_db)
):
    """
    获取部门列表
    
    - **skip**: 跳过记录数
    - **limit**: 限制记录数
    - **is_active**: 是否启用筛选
    """
    service = DepartmentService(db)
    return await service.get_departments(skip=skip, limit=limit, is_active=is_active)

@router.post("/", response_model=DepartmentResponse, status_code=status.HTTP_201_CREATED, summary="创建部门")
async def create_department(
    department: DepartmentCreate,
    db: Session = Depends(get_db)
):
    """创建新部门"""
    service = DepartmentService(db)
    return await service.create_department(department)

@router.get("/{department_id}", response_model=DepartmentResponse, summary="获取部门详情")
async def get_department(
    department_id: str,
    db: Session = Depends(get_db)
):
    """根据ID获取部门详情"""
    service = DepartmentService(db)
    department = await service.get_department_by_id(department_id)
    if not department:
        raise HTTPException(status_code=404, detail="部门不存在")
    return department

@router.put("/{department_id}", response_model=DepartmentResponse, summary="更新部门")
async def update_department(
    department_id: str,
    department: DepartmentUpdate,
    db: Session = Depends(get_db)
):
    """更新部门信息"""
    service = DepartmentService(db)
    updated_department = await service.update_department(department_id, department)
    if not updated_department:
        raise HTTPException(status_code=404, detail="部门不存在")
    return updated_department

@router.delete("/{department_id}", status_code=status.HTTP_204_NO_CONTENT, summary="删除部门")
async def delete_department(
    department_id: str,
    db: Session = Depends(get_db)
):
    """删除部门"""
    service = DepartmentService(db)
    success = await service.delete_department(department_id)
    if not success:
        raise HTTPException(status_code=404, detail="部门不存在")
```

#### 5.2.2 宿舍管理API

```python
# app/api/v1/dormitories.py
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.api.deps import get_db
from app.schemas.dormitory import DormitoryCreate, DormitoryUpdate, DormitoryResponse, DormitoryBedStatus
from app.services.dormitory_service import DormitoryService

router = APIRouter()

@router.get("/", response_model=List[DormitoryResponse], summary="获取宿舍列表")
async def get_dormitories(
    skip: int = 0,
    limit: int = 100,
    is_active: bool = None,
    db: Session = Depends(get_db)
):
    """获取宿舍列表"""
    service = DormitoryService(db)
    return await service.get_dormitories(skip=skip, limit=limit, is_active=is_active)

@router.post("/", response_model=DormitoryResponse, status_code=status.HTTP_201_CREATED, summary="创建宿舍")
async def create_dormitory(
    dormitory: DormitoryCreate,
    db: Session = Depends(get_db)
):
    """创建新宿舍"""
    service = DormitoryService(db)
    return await service.create_dormitory(dormitory)

@router.get("/{dormitory_id}/bed-status", response_model=DormitoryBedStatus, summary="获取宿舍床位状态")
async def get_dormitory_bed_status(
    dormitory_id: str,
    date: str = None,  # YYYY-MM-DD格式，默认为今天
    db: Session = Depends(get_db)
):
    """获取指定日期的宿舍床位状态"""
    service = DormitoryService(db)
    return await service.get_bed_status(dormitory_id, date)
```

#### 5.2.3 入住记录API

```python
# app/api/v1/records.py
from typing import List, Optional
from datetime import date
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from app.api.deps import get_db
from app.schemas.record import RecordCreate, RecordUpdate, RecordResponse, RecordFilter
from app.services.record_service import RecordService

router = APIRouter()

@router.get("/", response_model=List[RecordResponse], summary="获取入住记录列表")
async def get_records(
    skip: int = 0,
    limit: int = 100,
    dormitory_id: Optional[str] = Query(None, description="宿舍ID筛选"),
    department_id: Optional[str] = Query(None, description="部门ID筛选"),
    status: Optional[str] = Query(None, description="状态筛选"),
    check_in_start: Optional[date] = Query(None, description="入住开始日期"),
    check_in_end: Optional[date] = Query(None, description="入住结束日期"),
    db: Session = Depends(get_db)
):
    """
    获取入住记录列表，支持多种筛选条件
    """
    filter_params = RecordFilter(
        dormitory_id=dormitory_id,
        department_id=department_id,
        status=status,
        check_in_start=check_in_start,
        check_in_end=check_in_end
    )
    service = RecordService(db)
    return await service.get_records(skip=skip, limit=limit, filters=filter_params)

@router.post("/", response_model=RecordResponse, status_code=status.HTTP_201_CREATED, summary="创建入住记录")
async def create_record(
    record: RecordCreate,
    db: Session = Depends(get_db)
):
    """
    创建新的入住记录
    
    自动验证：
    - 床位是否存在
    - 床位是否在指定日期可用
    - 住户是否存在
    """
    service = RecordService(db)
    return await service.create_record(record)

@router.put("/{record_id}/checkout", response_model=RecordResponse, summary="办理离开")
async def checkout_record(
    record_id: str,
    checkout_date: date,
    notes: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """办理住户离开手续"""
    service = RecordService(db)
    return await service.checkout_record(record_id, checkout_date, notes)
```

#### 5.2.4 报表统计API

```python
# app/api/v1/reports.py
from typing import Optional
from datetime import date
from fastapi import APIRouter, Depends, HTTPException, Query, Response
from sqlalchemy.orm import Session

from app.api.deps import get_db
from app.schemas.report import MonthlyReportResponse, DailyAllocationResponse, ReportExportRequest
from app.services.report_service import ReportService

router = APIRouter()

@router.get("/monthly/{year}/{month}", response_model=MonthlyReportResponse, summary="获取月度报告")
async def get_monthly_report(
    year: int,
    month: int,
    end_date: Optional[date] = Query(None, description="截止日期，用于实时统计"),
    regenerate: bool = Query(False, description="是否重新生成报告"),
    db: Session = Depends(get_db)
):
    """
    获取指定年月的费用分摊报告
    
    - **year**: 年份
    - **month**: 月份 (1-12)
    - **end_date**: 截止日期，不指定则统计整月
    - **regenerate**: 是否强制重新计算
    """
    service = ReportService(db)
    return await service.get_monthly_report(year, month, end_date, regenerate)

@router.get("/daily", response_model=List[DailyAllocationResponse], summary="获取日度分摊明细")
async def get_daily_allocations(
    start_date: date = Query(..., description="开始日期"),
    end_date: date = Query(..., description="结束日期"),
    dormitory_id: Optional[str] = Query(None, description="宿舍ID筛选"),
    db: Session = Depends(get_db)
):
    """获取指定日期范围的日度分摊明细"""
    service = ReportService(db)
    return await service.get_daily_allocations(start_date, end_date, dormitory_id)

@router.post("/export", summary="导出报告")
async def export_report(
    export_request: ReportExportRequest,
    db: Session = Depends(get_db)
):
    """
    导出报告文件
    
    支持格式：
    - Excel (.xlsx)
    - PDF (.pdf)
    - CSV (.csv)
    """
    service = ReportService(db)
    file_content, filename, media_type = await service.export_report(export_request)
    
    return Response(
        content=file_content,
        media_type=media_type,
        headers={"Content-Disposition": f"attachment; filename={filename}"}
    )

@router.get("/realtime", response_model=MonthlyReportResponse, summary="获取实时报告")
async def get_realtime_report(
    db: Session = Depends(get_db)
):
    """获取当月实时报告（截止到今天）"""
    from datetime import datetime
    now = datetime.now()
    service = ReportService(db)
    return await service.get_monthly_report(now.year, now.month, now.date(), regenerate=True)
```

### 5.3 Pydantic数据模式设计

#### 5.3.1 基础模式类

```python
# app/schemas/base.py
from typing import Optional
from datetime import datetime
from pydantic import BaseModel, ConfigDict

class BaseSchema(BaseModel):
    """基础模式类"""
    model_config = ConfigDict(from_attributes=True)

class TimestampMixin(BaseModel):
    """时间戳混入类"""
    created_at: datetime
    updated_at: datetime

class IDMixin(BaseModel):
    """ID混入类"""
    id: str
```

#### 5.3.2 部门相关模式

```python
# app/schemas/department.py
from typing import Optional
from pydantic import BaseModel, Field
from .base import BaseSchema, TimestampMixin, IDMixin

class DepartmentBase(BaseModel):
    """部门基础模式"""
    name: str = Field(..., min_length=1, max_length=100, description="部门名称")
    description: Optional[str] = Field(None, max_length=500, description="部门描述")
    is_active: bool = Field(True, description="是否启用")

class DepartmentCreate(DepartmentBase):
    """创建部门模式"""
    pass

class DepartmentUpdate(BaseModel):
    """更新部门模式"""
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="部门名称")
    description: Optional[str] = Field(None, max_length=500, description="部门描述")
    is_active: Optional[bool] = Field(None, description="是否启用")

class DepartmentResponse(DepartmentBase, IDMixin, TimestampMixin):
    """部门响应模式"""
    resident_count: Optional[int] = Field(None, description="住户数量")
```

#### 5.3.3 报表相关模式

```python
# app/schemas/report.py
from typing import List, Dict, Optional
from datetime import date
from pydantic import BaseModel, Field
from .base import BaseSchema, IDMixin

class DepartmentSummary(BaseModel):
    """部门汇总信息"""
    department_id: str = Field(..., description="部门ID")
    department_name: str = Field(..., description="部门名称")
    bed_days: float = Field(..., ge=0, description="床位天数")
    cost: float = Field(..., ge=0, description="费用金额")
    ratio: float = Field(..., ge=0, le=1, description="分摊比例")

class DailyAllocationDetail(BaseModel):
    """日度分摊明细"""
    date: date = Field(..., description="日期")
    dormitory_id: str = Field(..., description="宿舍ID")
    dormitory_name: str = Field(..., description="宿舍名称")
    total_beds: int = Field(..., gt=0, description="总床位数")
    occupied_beds: int = Field(..., ge=0, description="已占用床位数")
    department_allocations: List[Dict] = Field(..., description="部门分摊详情")

class MonthlyReportResponse(BaseSchema, IDMixin):
    """月度报告响应模式"""
    year: int = Field(..., ge=1900, le=3000, description="年份")
    month: int = Field(..., ge=1, le=12, description="月份")
    report_date: date = Field(..., description="报告日期")
    total_bed_days: float = Field(..., ge=0, description="总床位天数")
    department_summary: List[DepartmentSummary] = Field(..., description="部门汇总")
    daily_details: List[DailyAllocationDetail] = Field(..., description="日度明细")
    
class ReportExportRequest(BaseModel):
    """报告导出请求"""
    report_type: str = Field(..., regex="^(monthly|daily)$", description="报告类型")
    format: str = Field(..., regex="^(excel|pdf|csv)$", description="导出格式")
    year: int = Field(..., ge=1900, le=3000, description="年份")
    month: int = Field(..., ge=1, le=12, description="月份")
    end_date: Optional[date] = Field(None, description="截止日期")
```

## 6. 业务服务层设计

### 6.1 分摊计算核心服务

```python
# app/services/allocation_calculator.py
from typing import List, Dict, Optional
from datetime import date, timedelta
from collections import defaultdict
from dataclasses import dataclass

from app.models.record import ResidenceRecord
from app.models.dormitory import Dormitory
from app.schemas.report import DepartmentSummary, DailyAllocationDetail

@dataclass
class DepartmentAllocation:
    """部门分摊信息"""
    department_id: str
    department_name: str
    bed_count: int
    allocation_ratio: float

@dataclass
class DailyAllocationResult:
    """日度分摊结果"""
    date: date
    dormitory_id: str
    dormitory_name: str
    total_beds: int
    occupied_beds: int
    department_allocations: List[DepartmentAllocation]

class AllocationCalculator:
    """费用分摊计算器"""
    
    def __init__(self, unit_cost_per_bed_day: float = 100.0):
        """
        初始化计算器
        
        Args:
            unit_cost_per_bed_day: 每床每日费用单价
        """
        self.unit_cost = unit_cost_per_bed_day
    
    def calculate_daily_allocation(
        self, 
        target_date: date, 
        dormitory: Dormitory,
        residence_records: List[ResidenceRecord]
    ) -> DailyAllocationResult:
        """
        计算指定日期指定宿舍的分摊结果
        
        Args:
            target_date: 目标计算日期
            dormitory: 宿舍信息
            residence_records: 入住记录列表
            
        Returns:
            DailyAllocationResult: 日度分摊结果
        """
        # 1. 筛选有效的入住记录
        active_records = self._filter_active_records(target_date, dormitory.id, residence_records)
        
        # 2. 按部门分组统计
        department_stats = self._group_by_department(active_records)
        
        # 3. 计算分摊比例
        allocations = self._calculate_allocations(department_stats, dormitory.total_beds)
        
        return DailyAllocationResult(
            date=target_date,
            dormitory_id=dormitory.id,
            dormitory_name=dormitory.name,
            total_beds=dormitory.total_beds,
            occupied_beds=len(active_records),
            department_allocations=allocations
        )
    
    def calculate_monthly_summary(
        self, 
        year: int, 
        month: int,
        daily_allocations: List[DailyAllocationResult],
        end_date: Optional[date] = None
    ) -> List[DepartmentSummary]:
        """
        计算月度部门费用汇总
        
        Args:
            year: 年份
            month: 月份
            daily_allocations: 日度分摊结果列表
            end_date: 截止日期
            
        Returns:
            List[DepartmentSummary]: 部门费用汇总列表
        """
        # 汇总各部门床位天数
        department_bed_days = defaultdict(float)
        department_names = {}
        
        for daily_allocation in daily_allocations:
            for dept_alloc in daily_allocation.department_allocations:
                # 计算该部门在该日该宿舍的床位天数
                bed_days = daily_allocation.total_beds * dept_alloc.allocation_ratio
                department_bed_days[dept_alloc.department_id] += bed_days
                department_names[dept_alloc.department_id] = dept_alloc.department_name
        
        # 计算总床位天数
        total_bed_days = sum(department_bed_days.values())
        
        # 生成部门汇总列表
        summaries = []
        for dept_id, bed_days in department_bed_days.items():
            cost = bed_days * self.unit_cost
            ratio = bed_days / total_bed_days if total_bed_days > 0 else 0
            
            summaries.append(DepartmentSummary(
                department_id=dept_id,
                department_name=department_names[dept_id],
                bed_days=round(bed_days, 2),
                cost=round(cost, 2),
                ratio=round(ratio, 4)
            ))
        
        # 按床位天数降序排序
        summaries.sort(key=lambda x: x.bed_days, reverse=True)
        return summaries
    
    def _filter_active_records(
        self, 
        target_date: date, 
        dormitory_id: str,
        records: List[ResidenceRecord]
    ) -> List[ResidenceRecord]:
        """筛选指定日期在指定宿舍的有效入住记录"""
        active_records = []
        for record in records:
            if (record.dormitory_id == dormitory_id and 
                record.status == 'ACTIVE' and
                record.check_in_date <= target_date and
                (record.check_out_date is None or record.check_out_date > target_date)):
                active_records.append(record)
        return active_records
    
    def _group_by_department(self, records: List[ResidenceRecord]) -> Dict[str, Dict[str, int]]:
        """按部门分组统计住户数量"""
        department_stats = defaultdict(lambda: {"count": 0, "name": ""})
        
        for record in records:
            dept_id = record.resident.department_id
            dept_name = record.resident.department.name
            department_stats[dept_id]["count"] += 1
            department_stats[dept_id]["name"] = dept_name
            
        return dict(department_stats)
    
    def _calculate_allocations(
        self, 
        department_stats: Dict[str, Dict[str, int]], 
        total_beds: int
    ) -> List[DepartmentAllocation]:
        """计算各部门分摊比例"""
        allocations = []
        
        if not department_stats:
            # 空宿舍场景 - 公司承担100%
            allocations.append(DepartmentAllocation(
                department_id="company",
                department_name="公司",
                bed_count=total_beds,
                allocation_ratio=1.0
            ))
        else:
            # 有人入住场景 - 按部门数量平均分摊
            department_count = len(department_stats)
            ratio_per_department = 1.0 / department_count
            
            for dept_id, stats in department_stats.items():
                allocations.append(DepartmentAllocation(
                    department_id=dept_id,
                    department_name=stats["name"],
                    bed_count=stats["count"],
                    allocation_ratio=ratio_per_department
                ))
        
        return allocations
    
    def get_date_range(self, start_date: date, end_date: date) -> List[date]:
        """生成日期范围"""
        dates = []
        current_date = start_date
        while current_date <= end_date:
            dates.append(current_date)
            current_date += timedelta(days=1)
        return dates
    
    def get_month_date_range(
        self, 
        year: int, 
        month: int, 
        end_date: Optional[date] = None
    ) -> List[date]:
        """获取月份日期范围"""
        # 月份开始日期
        month_start = date(year, month, 1)
        
        # 月份自然结束日期
        if month == 12:
            next_month = date(year + 1, 1, 1)
        else:
            next_month = date(year, month + 1, 1)
        month_end = next_month - timedelta(days=1)
        
        # 确定实际结束日期
        actual_end = min(end_date, month_end) if end_date else month_end
        
        return self.get_date_range(month_start, actual_end)
```

### 6.2 报表服务

```python
# app/services/report_service.py
from typing import List, Optional, Tuple
from datetime import date, datetime
from sqlalchemy.orm import Session

from app.models.dormitory import Dormitory
from app.models.record import ResidenceRecord
from app.models.report import MonthlyReport, DailyAllocation
from app.repositories.report_repo import ReportRepository
from app.repositories.dormitory_repo import DormitoryRepository
from app.repositories.record_repo import RecordRepository
from app.services.allocation_calculator import AllocationCalculator
from app.schemas.report import MonthlyReportResponse, DailyAllocationResponse
from app.utils.export_utils import ExportUtils
from app.core.logging import get_logger

logger = get_logger(__name__)

class ReportService:
    """报表服务"""
    
    def __init__(self, db: Session):
        self.db = db
        self.report_repo = ReportRepository(db)
        self.dormitory_repo = DormitoryRepository(db)
        self.record_repo = RecordRepository(db)
        self.calculator = AllocationCalculator()
        self.export_utils = ExportUtils()
    
    async def get_monthly_report(
        self, 
        year: int, 
        month: int, 
        end_date: Optional[date] = None,
        regenerate: bool = False
    ) -> MonthlyReportResponse:
        """
        获取月度报告
        
        Args:
            year: 年份
            month: 月份
            end_date: 截止日期，用于实时统计
            regenerate: 是否强制重新生成
            
        Returns:
            MonthlyReportResponse: 月度报告
        """
        logger.info(f"开始生成月度报告: {year}年{month}月, 截止日期: {end_date}")
        
        # 检查是否已存在报告且不需要重新生成
        if not regenerate and end_date is None:
            existing_report = await self.report_repo.get_by_year_month(year, month)
            if existing_report:
                logger.info(f"使用已存在的月度报告: {existing_report.id}")
                return MonthlyReportResponse.model_validate(existing_report)
        
        # 生成新的月度报告
        start_time = datetime.now()
        
        # 1. 获取日期范围
        date_range = self.calculator.get_month_date_range(year, month, end_date)
        
        # 2. 获取所有宿舍和入住记录
        dormitories = await self.dormitory_repo.get_active_dormitories()
        residence_records = await self.record_repo.get_records_in_date_range(
            date_range[0], date_range[-1]
        )
        
        # 3. 计算每日分摊
        daily_allocations = []
        for target_date in date_range:
            for dormitory in dormitories:
                daily_result = self.calculator.calculate_daily_allocation(
                    target_date, dormitory, residence_records
                )
                daily_allocations.append(daily_result)
        
        # 4. 计算月度汇总
        department_summary = self.calculator.calculate_monthly_summary(
            year, month, daily_allocations, end_date
        )
        
        # 5. 保存报告（仅完整月份）
        if end_date is None:
            report = await self.report_repo.create_monthly_report(
                year=year,
                month=month,
                total_bed_days=sum(s.bed_days for s in department_summary),
                department_summary=[s.model_dump() for s in department_summary],
                daily_details=[self._daily_result_to_dict(d) for d in daily_allocations]
            )
            report_id = report.id
        else:
            report_id = None
        
        # 6. 记录计算耗时
        duration = (datetime.now() - start_time).total_seconds()
        logger.info(f"月度报告生成完成，耗时: {duration:.2f}秒")
        
        return MonthlyReportResponse(
            id=report_id or "temp",
            year=year,
            month=month,
            report_date=end_date or date_range[-1],
            total_bed_days=sum(s.bed_days for s in department_summary),
            department_summary=department_summary,
            daily_details=[self._daily_result_to_detail(d) for d in daily_allocations],
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
    
    async def export_report(self, export_request) -> Tuple[bytes, str, str]:
        """
        导出报告文件
        
        Args:
            export_request: 导出请求参数
            
        Returns:
            Tuple[bytes, str, str]: (文件内容, 文件名, MIME类型)
        """
        # 获取报告数据
        report = await self.get_monthly_report(
            export_request.year, 
            export_request.month, 
            export_request.end_date
        )
        
        # 根据格式导出
        if export_request.format == "excel":
            return await self.export_utils.export_to_excel(report)
        elif export_request.format == "pdf":
            return await self.export_utils.export_to_pdf(report)
        elif export_request.format == "csv":
            return await self.export_utils.export_to_csv(report)
        else:
            raise ValueError(f"不支持的导出格式: {export_request.format}")
    
    def _daily_result_to_dict(self, daily_result) -> dict:
        """将日度结果转换为字典"""
        return {
            "date": daily_result.date.isoformat(),
            "dormitory_id": daily_result.dormitory_id,
            "dormitory_name": daily_result.dormitory_name,
            "total_beds": daily_result.total_beds,
            "occupied_beds": daily_result.occupied_beds,
            "department_allocations": [
                {
                    "department_id": alloc.department_id,
                    "department_name": alloc.department_name,
                    "bed_count": alloc.bed_count,
                    "allocation_ratio": alloc.allocation_ratio
                }
                for alloc in daily_result.department_allocations
            ]
        }
    
    def _daily_result_to_detail(self, daily_result) -> DailyAllocationDetail:
        """将日度结果转换为响应模型"""
        return DailyAllocationDetail(
            date=daily_result.date,
            dormitory_id=daily_result.dormitory_id,
            dormitory_name=daily_result.dormitory_name,
            total_beds=daily_result.total_beds,
            occupied_beds=daily_result.occupied_beds,
            department_allocations=[
                {
                    "department_id": alloc.department_id,
                    "department_name": alloc.department_name,
                    "bed_count": alloc.bed_count,
                    "allocation_ratio": alloc.allocation_ratio
                }
                for alloc in daily_result.department_allocations
            ]
        )
```

## 7. 前端架构设计

### 7.1 Vue 3组件架构

```mermaid
graph TD
    A[App.vue] --> B[Layout组件]
    B --> C[Header组件]
    B --> D[Sidebar组件]
    B --> E[Main内容区]
    
    E --> F[Dashboard页面]
    E --> G[部门管理页面]
    E --> H[宿舍管理页面]
    E --> I[住户管理页面]
    E --> J[入住记录页面]
    E --> K[报表分析页面]
    
    G --> G1[部门列表组件]
    G --> G2[部门表单组件]
    
    H --> H1[宿舍列表组件]
    H --> H2[床位状态组件]
    
    I --> I1[住户列表组件]
    I --> I2[住户表单组件]
    
    J --> J1[记录列表组件]
    J --> J2[入住表单组件]
    J --> J3[离开表单组件]
    
    K --> K1[月度报表组件]
    K --> K2[图表展示组件]
    K --> K3[导出功能组件]
```

### 7.2 Pinia状态管理设计

```javascript
// frontend/stores/dormitory.js
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { dormitoryAPI } from '@/utils/api'

export const useDormitoryStore = defineStore('dormitory', () => {
  // 状态
  const dormitories = ref([])
  const currentDormitory = ref(null)
  const loading = ref(false)
  const error = ref(null)
  
  // 计算属性
  const activeDormitories = computed(() => 
    dormitories.value.filter(d => d.is_active)
  )
  
  const totalBeds = computed(() => 
    activeDormitories.value.reduce((sum, d) => sum + d.total_beds, 0)
  )
  
  // 方法
  const fetchDormitories = async () => {
    loading.value = true
    error.value = null
    try {
      const response = await dormitoryAPI.getList()
      dormitories.value = response.data
    } catch (err) {
      error.value = err.message
      console.error('获取宿舍列表失败:', err)
    } finally {
      loading.value = false
    }
  }
  
  const createDormitory = async (dormitoryData) => {
    loading.value = true
    try {
      const response = await dormitoryAPI.create(dormitoryData)
      dormitories.value.push(response.data)
      return response.data
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }
  
  const updateDormitory = async (id, dormitoryData) => {
    loading.value = true
    try {
      const response = await dormitoryAPI.update(id, dormitoryData)
      const index = dormitories.value.findIndex(d => d.id === id)
      if (index !== -1) {
        dormitories.value[index] = response.data
      }
      return response.data
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }
  
  const deleteDormitory = async (id) => {
    loading.value = true
    try {
      await dormitoryAPI.delete(id)
      dormitories.value = dormitories.value.filter(d => d.id !== id)
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }
  
  const getBedStatus = async (dormitoryId, date = null) => {
    try {
      const response = await dormitoryAPI.getBedStatus(dormitoryId, date)
      return response.data
    } catch (err) {
      error.value = err.message
      throw err
    }
  }
  
  return {
    // 状态
    dormitories,
    currentDormitory,
    loading,
    error,
    
    // 计算属性
    activeDormitories,
    totalBeds,
    
    // 方法
    fetchDormitories,
    createDormitory,
    updateDormitory,
    deleteDormitory,
    getBedStatus
  }
})
```

### 7.3 核心组件设计

#### 7.3.1 月度报表组件

```vue
<!-- frontend/components/report/MonthlyReport.vue -->
<template>
  <div class="monthly-report bg-white rounded-lg shadow-lg p-6">
    <!-- 报表头部 -->
    <div class="report-header mb-6">
      <div class="flex justify-between items-center">
        <h2 class="text-2xl font-bold text-gray-900">
          {{ year }}年{{ month }}月 宿舍费用分摊报告
        </h2>
        <div class="flex space-x-3">
          <button
            @click="refreshReport"
            :disabled="loading"
            class="btn btn-secondary"
          >
            <RefreshIcon class="w-4 h-4 mr-2" />
            刷新
          </button>
          <button
            @click="showExportModal = true"
            class="btn btn-primary"
          >
            <DownloadIcon class="w-4 h-4 mr-2" />
            导出
          </button>
        </div>
      </div>
      
      <!-- 时间选择器 -->
      <div class="mt-4 flex items-center space-x-4">
        <div class="flex items-center space-x-2">
          <label class="text-sm font-medium text-gray-700">年月:</label>
          <input
            type="month"
            v-model="selectedMonth"
            @change="onMonthChange"
            class="form-input"
          />
        </div>
        <div class="flex items-center space-x-2">
          <label class="text-sm font-medium text-gray-700">截止日期:</label>
          <input
            type="date"
            v-model="endDate"
            @change="onEndDateChange"
            class="form-input"
          />
          <button
            @click="clearEndDate"
            class="text-sm text-gray-500 hover:text-gray-700"
          >
            清除
          </button>
        </div>
      </div>
    </div>
    
    <!-- 加载状态 -->
    <div v-if="loading" class="text-center py-8">
      <LoadingSpinner />
      <p class="mt-2 text-gray-600">正在生成报告...</p>
    </div>
    
    <!-- 错误状态 -->
    <div v-else-if="error" class="text-center py-8">
      <AlertTriangleIcon class="w-16 h-16 text-red-500 mx-auto mb-4" />
      <p class="text-red-600">{{ error }}</p>
      <button @click="refreshReport" class="mt-4 btn btn-primary">
        重试
      </button>
    </div>
    
    <!-- 报告内容 -->
    <div v-else-if="report" class="report-content">
      <!-- 汇总信息 -->
      <div class="summary-section mb-8">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">费用汇总</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div class="stat-card">
            <div class="stat-label">总床位天数</div>
            <div class="stat-value">{{ report.total_bed_days.toFixed(1) }}</div>
          </div>
          <div class="stat-card">
            <div class="stat-label">总费用金额</div>
            <div class="stat-value">¥{{ totalCost.toLocaleString() }}</div>
          </div>
          <div class="stat-card">
            <div class="stat-label">涉及部门数</div>
            <div class="stat-value">{{ report.department_summary.length }}</div>
          </div>
        </div>
      </div>
      
      <!-- 部门分摊详情 -->
      <div class="department-section mb-8">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">部门分摊详情</h3>
        <div class="overflow-x-auto">
          <table class="min-w-full table-auto">
            <thead class="bg-gray-50">
              <tr>
                <th class="table-header">部门名称</th>
                <th class="table-header">床位天数</th>
                <th class="table-header">费用金额</th>
                <th class="table-header">分摊比例</th>
                <th class="table-header">图表</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr
                v-for="dept in report.department_summary"
                :key="dept.department_id"
                class="hover:bg-gray-50"
              >
                <td class="table-cell font-medium">{{ dept.department_name }}</td>
                <td class="table-cell">{{ dept.bed_days.toFixed(1) }}</td>
                <td class="table-cell">¥{{ dept.cost.toLocaleString() }}</td>
                <td class="table-cell">
                  <div class="flex items-center">
                    <span class="mr-2">{{ (dept.ratio * 100).toFixed(2) }}%</span>
                    <div class="flex-1 bg-gray-200 rounded-full h-2">
                      <div
                        class="bg-blue-600 h-2 rounded-full"
                        :style="{ width: `${dept.ratio * 100}%` }"
                      ></div>
                    </div>
                  </div>
                </td>
                <td class="table-cell">
                  <button
                    @click="showDepartmentDetail(dept)"
                    class="text-blue-600 hover:text-blue-800"
                  >
                    查看详情
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      
      <!-- 图表展示 -->
      <div class="chart-section mb-8">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">分摊比例图表</h3>
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div class="chart-container">
            <h4 class="chart-title">费用分摊饼图</h4>
            <PieChart :data="pieChartData" />
          </div>
          <div class="chart-container">
            <h4 class="chart-title">床位天数柱状图</h4>
            <BarChart :data="barChartData" />
          </div>
        </div>
      </div>
    </div>
    
    <!-- 导出模态框 -->
    <ExportModal
      v-if="showExportModal"
      :year="year"
      :month="month"
      :end-date="endDate"
      @close="showExportModal = false"
      @export="handleExport"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useReportStore } from '@/stores/report'
import { useToast } from '@/composables/useToast'
import {
  RefreshIcon,
  DownloadIcon,
  AlertTriangleIcon
} from '@heroicons/vue/24/outline'
import LoadingSpinner from '@/components/common/LoadingSpinner.vue'
import PieChart from '@/components/common/PieChart.vue'
import BarChart from '@/components/common/BarChart.vue'
import ExportModal from '@/components/report/ExportModal.vue'

// Props
const props = defineProps({
  initialYear: {
    type: Number,
    default: () => new Date().getFullYear()
  },
  initialMonth: {
    type: Number,
    default: () => new Date().getMonth() + 1
  }
})

// 响应式数据
const reportStore = useReportStore()
const { showToast } = useToast()

const year = ref(props.initialYear)
const month = ref(props.initialMonth)
const endDate = ref(null)
const selectedMonth = ref(`${year.value}-${month.value.toString().padStart(2, '0')}`)
const showExportModal = ref(false)

// 计算属性
const report = computed(() => reportStore.currentReport)
const loading = computed(() => reportStore.loading)
const error = computed(() => reportStore.error)

const totalCost = computed(() => {
  if (!report.value) return 0
  return report.value.department_summary.reduce((sum, dept) => sum + dept.cost, 0)
})

const pieChartData = computed(() => {
  if (!report.value) return []
  return report.value.department_summary.map(dept => ({
    label: dept.department_name,
    value: dept.cost,
    percentage: dept.ratio * 100
  }))
})

const barChartData = computed(() => {
  if (!report.value) return []
  return {
    labels: report.value.department_summary.map(dept => dept.department_name),
    datasets: [{
      label: '床位天数',
      data: report.value.department_summary.map(dept => dept.bed_days),
      backgroundColor: 'rgba(59, 130, 246, 0.8)',
      borderColor: 'rgba(59, 130, 246, 1)',
      borderWidth: 1
    }]
  }
})

// 方法
const loadReport = async () => {
  try {
    await reportStore.fetchMonthlyReport(year.value, month.value, endDate.value)
  } catch (err) {
    showToast('报告生成失败', 'error')
  }
}

const refreshReport = async () => {
  try {
    await reportStore.fetchMonthlyReport(year.value, month.value, endDate.value, true)
    showToast('报告刷新成功', 'success')
  } catch (err) {
    showToast('报告刷新失败', 'error')
  }
}

const onMonthChange = () => {
  const [newYear, newMonth] = selectedMonth.value.split('-')
  year.value = parseInt(newYear)
  month.value = parseInt(newMonth)
  loadReport()
}

const onEndDateChange = () => {
  loadReport()
}

const clearEndDate = () => {
  endDate.value = null
  loadReport()
}

const showDepartmentDetail = (department) => {
  // 显示部门详情模态框
  console.log('显示部门详情:', department)
}

const handleExport = async (exportOptions) => {
  try {
    await reportStore.exportReport(exportOptions)
    showToast('报告导出成功', 'success')
    showExportModal.value = false
  } catch (err) {
    showToast('报告导出失败', 'error')
  }
}

// 生命周期
onMounted(() => {
  loadReport()
})

// 监听器
watch([year, month], () => {
  selectedMonth.value = `${year.value}-${month.value.toString().padStart(2, '0')}`
})
</script>

<style scoped>
.btn {
  @apply px-4 py-2 rounded-md font-medium transition-colors duration-200;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700;
}

.btn-secondary {
  @apply bg-gray-600 text-white hover:bg-gray-700;
}

.form-input {
  @apply px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500;
}

.stat-card {
  @apply bg-gradient-to-r from-blue-50 to-blue-100 p-6 rounded-lg;
}

.stat-label {
  @apply text-sm font-medium text-gray-600 mb-2;
}

.stat-value {
  @apply text-3xl font-bold text-blue-600;
}

.table-header {
  @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
}

.table-cell {
  @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
}

.chart-container {
  @apply bg-gray-50 p-4 rounded-lg;
}

.chart-title {
  @apply text-base font-medium text-gray-900 mb-4;
}
</style>
```

## 8. 部署和运维设计

### 8.1 应用配置管理

```python
# app/core/config.py
from typing import Optional
from pydantic import BaseSettings, validator
import os

class Settings(BaseSettings):
    """应用配置"""
    
    # 应用基本配置
    APP_NAME: str = "宿舍入住管理系统"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = False
    SECRET_KEY: str
    
    # 数据库配置
    DATABASE_URL: str = "sqlite:///./bedsharing.db"
    DATABASE_ECHO: bool = False
    
    # API配置
    API_V1_PREFIX: str = "/api/v1"
    BACKEND_CORS_ORIGINS: list = ["http://localhost:3000", "http://localhost:8080"]
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "logs/app.log"
    
    # 文件上传配置
    UPLOAD_MAX_SIZE: int = 10 * 1024 * 1024  # 10MB
    UPLOAD_ALLOWED_EXTENSIONS: set = {".xlsx", ".xls", ".csv"}
    
    # 导出配置
    EXPORT_MAX_RECORDS: int = 10000
    EXPORT_TIMEOUT: int = 300  # 5分钟
    
    # 分摊计算配置
    UNIT_COST_PER_BED_DAY: float = 100.0
    DEFAULT_COMPANY_DEPARTMENT: str = "公司"
    
    # Redis配置（缓存）
    REDIS_URL: Optional[str] = None
    CACHE_TTL: int = 3600  # 1小时
    
    @validator("BACKEND_CORS_ORIGINS", pre=True)
    def assemble_cors_origins(cls, v):
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)
    
    class Config:
        env_file = ".env"
        case_sensitive = True

settings = Settings()
```

### 8.2 Docker部署配置

```dockerfile
# Dockerfile
FROM node:18-alpine AS frontend-builder

# 构建前端
WORKDIR /app/frontend
COPY frontend/package*.json ./
RUN npm ci --only=production

COPY frontend/ ./
RUN npm run build

# Python应用
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# 安装Python依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY app/ ./app/
COPY alembic.ini ./
COPY migrations/ ./migrations/

# 复制前端构建结果
COPY --from=frontend-builder /app/frontend/dist ./static/

# 创建日志目录
RUN mkdir -p logs

# 设置环境变量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

```yaml
# docker-compose.yml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=****************************************/bedsharing
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=your-secret-key-here
    depends_on:
      - db
      - redis
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/uploads
    restart: unless-stopped

  db:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=bedsharing
      - POSTGRES_USER=bedsharing
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    restart: unless-stopped

volumes:
  postgres_data:
```

### 8.3 监控和日志配置

```python
# app/core/logging.py
import logging
import logging.handlers
from pathlib import Path
from app.core.config import settings

def setup_logging():
    """配置日志系统"""
    
    # 创建日志目录
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # 配置根日志器
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, settings.LOG_LEVEL))
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_format = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    console_handler.setFormatter(console_format)
    
    # 文件处理器（带滚动）
    file_handler = logging.handlers.RotatingFileHandler(
        settings.LOG_FILE,
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    file_handler.setLevel(logging.DEBUG)
    file_format = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
    )
    file_handler.setFormatter(file_format)
    
    # 添加处理器
    root_logger.addHandler(console_handler)
    root_logger.addHandler(file_handler)
    
    # 配置第三方库日志级别
    logging.getLogger("uvicorn").setLevel(logging.INFO)
    logging.getLogger("sqlalchemy.engine").setLevel(logging.WARNING)
    
    return root_logger

def get_logger(name: str) -> logging.Logger:
    """获取指定名称的日志器"""
    return logging.getLogger(name)

# 业务日志记录器
class BusinessLogger:
    """业务操作日志记录器"""
    
    def __init__(self):
        self.logger = get_logger("business")
    
    def log_user_action(self, user_id: str, action: str, details: dict = None):
        """记录用户操作"""
        message = f"用户 {user_id} 执行操作: {action}"
        if details:
            message += f", 详情: {details}"
        self.logger.info(message)
    
    def log_calculation_start(self, year: int, month: int, user_id: str = None):
        """记录分摊计算开始"""
        message = f"开始计算 {year}年{month}月 分摊报告"
        if user_id:
            message += f", 操作用户: {user_id}"
        self.logger.info(message)
    
    def log_calculation_end(self, year: int, month: int, duration: float, user_id: str = None):
        """记录分摊计算结束"""
        message = f"完成计算 {year}年{month}月 分摊报告，耗时 {duration:.2f}秒"
        if user_id:
            message += f", 操作用户: {user_id}"
        self.logger.info(message)
    
    def log_export_action(self, report_type: str, format: str, user_id: str = None):
        """记录报告导出"""
        message = f"导出{report_type}报告，格式: {format}"
        if user_id:
            message += f", 操作用户: {user_id}"
        self.logger.info(message)
    
    def log_data_import(self, file_name: str, record_count: int, user_id: str = None):
        """记录数据导入"""
        message = f"导入数据文件: {file_name}, 记录数: {record_count}"
        if user_id:
            message += f", 操作用户: {user_id}"
        self.logger.info(message)
    
    def log_error(self, operation: str, error: Exception, user_id: str = None):
        """记录错误信息"""
        message = f"操作 {operation} 发生错误: {str(error)}"
        if user_id:
            message += f", 操作用户: {user_id}"
        self.logger.error(message, exc_info=True)

# 全局业务日志器实例
business_logger = BusinessLogger()
```

## 9. 总结

本技术设计方案提供了宿舍入住管理系统的完整解决方案，具备以下特点：

### 9.1 技术特色

- **现代化技术栈**：采用FastAPI + Vue 3 + Tailwind CSS构建
- **科学分摊算法**：基于需求文档实现精确的费用分摊计算
- **完善的数据模型**：支持复杂业务场景和数据完整性约束
- **灵活的API设计**：RESTful风格，支持多种查询和导出需求
- **优雅的前端界面**：响应式设计，用户体验友好

### 9.2 系统优势

- **可维护性强**：清晰的代码结构和完善的文档
- **扩展性好**：支持功能扩展和性能优化
- **部署简便**：Docker化部署，支持多环境配置
- **监控完善**：全面的日志记录和错误追踪

### 9.3 开发计划

基于本设计方案，开发团队可以按照以下顺序进行实施：

1. **第一阶段**：数据模型和API接口开发
2. **第二阶段**：核心业务逻辑和分摊算法实现
3. **第三阶段**：前端界面和交互功能开发
4. **第四阶段**：测试、优化和部署

该设计方案为后续的代码实现提供了详细的技术指导，确保系统能够满足业务需求并具备良好的技术质量。 